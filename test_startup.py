#!/usr/bin/env python3
"""
Test script to diagnose startup issues step by step.
"""

import os
import sys

print("🔍 Step 1: Testing basic imports...")
try:
    from flask import Flask
    print("✅ Flask import successful")
except Exception as e:
    print(f"❌ Flask import failed: {e}")
    sys.exit(1)

print("🔍 Step 2: Testing database connection...")
try:
    import pymysql
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='@2494/lK',
        database='hillview_demo001',
        charset='utf8mb4',
        connect_timeout=5  # 5 second timeout
    )
    print("✅ Database connection successful")
    connection.close()
except Exception as e:
    print(f"❌ Database connection failed: {e}")

print("🔍 Step 3: Testing SQLAlchemy...")
try:
    from flask_sqlalchemy import SQLAlchemy
    print("✅ SQLAlchemy import successful")
except Exception as e:
    print(f"❌ SQLAlchemy import failed: {e}")

print("🔍 Step 4: Testing extensions...")
try:
    from extensions import db, csrf
    print("✅ Extensions import successful")
except Exception as e:
    print(f"❌ Extensions import failed: {e}")

print("🔍 Step 5: Creating minimal Flask app...")
try:
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@2494/lK@localhost:3306/hillview_demo001?charset=utf8mb4'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    @app.route('/')
    def test():
        return "✅ Flask app created successfully!"
    
    print("✅ Minimal Flask app created")
    
    # Test if we can start the server
    print("🌐 Testing server startup...")
    print("📍 Server would start at: http://localhost:5001")
    print("✅ All tests passed! The issue might be with complex imports or database initialization.")
    
except Exception as e:
    print(f"❌ Flask app creation failed: {e}")
    import traceback
    traceback.print_exc()

print("🎯 Diagnosis complete!")
