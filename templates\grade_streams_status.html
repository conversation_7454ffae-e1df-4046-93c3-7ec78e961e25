<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grade {{ grade }} Streams Status - Hillview School</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            padding: 30px;
        }
        .header-section {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .header-title {
            color: #2c3e50;
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .header-subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(52, 152, 219, 0.4);
        }
        .summary-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .summary-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .streams-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .stream-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .stream-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        .stream-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .stream-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .status-complete {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
        .status-partial {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        .status-none {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }
        .progress-section {
            margin-bottom: 20px;
        }
        .progress {
            height: 12px;
            border-radius: 10px;
            background: #e9ecef;
            overflow: hidden;
        }
        .progress-bar {
            background: linear-gradient(135deg, #27ae60, #229954);
            transition: width 0.6s ease;
        }
        .stream-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .grade-actions {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            text-align: center;
        }
        .grade-actions h4 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 20px;
        }
        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: #2c3e50;
        }
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }
        .stream-selection {
            margin: 20px 0;
        }
        .form-check {
            margin-bottom: 10px;
        }
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mt-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('classteacher.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('classteacher.grade_reports_dashboard') }}">Grade Reports</a></li>
                <li class="breadcrumb-item active">{{ grade }} - {{ term }} - {{ assessment_type }}</li>
            </ol>
        </nav>

        <div class="dashboard-container">
            <!-- Header -->
            <div class="header-section">
                <h1 class="header-title">
                    <i class="fas fa-layer-group me-3"></i>
                    Grade {{ grade }} Streams Status
                </h1>
                <p class="header-subtitle">
                    {{ term.replace('_', ' ').title() }} - {{ assessment_type.replace('_', ' ').title() }}
                </p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Summary Cards -->
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-number">{{ total_streams }}</div>
                    <div class="summary-label">Total Streams</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{{ streams_with_marks }}</div>
                    <div class="summary-label">Streams with Marks</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{{ total_students }}</div>
                    <div class="summary-label">Total Students</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">{{ "%.1f"|format(overall_completion) }}%</div>
                    <div class="summary-label">Overall Completion</div>
                </div>
            </div>

            <!-- Streams Grid -->
            <div class="streams-grid">
                {% for stream in streams %}
                <div class="stream-card">
                    <div class="stream-header">
                        <h3 class="stream-title">{{ stream.display_name }}</h3>
                        <span class="status-badge status-{{ 'complete' if stream.completion_percentage >= 80 else 'partial' if stream.has_marks else 'none' }}">
                            {% if stream.completion_percentage >= 80 %}
                                <i class="fas fa-check-circle me-1"></i>Complete
                            {% elif stream.has_marks %}
                                <i class="fas fa-clock me-1"></i>Partial
                            {% else %}
                                <i class="fas fa-times-circle me-1"></i>No Marks
                            {% endif %}
                        </span>
                    </div>

                    <div class="progress-section">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Completion Progress</span>
                            <span>{{ "%.1f"|format(stream.completion_percentage) }}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ stream.completion_percentage }}%"></div>
                        </div>
                    </div>

                    <div class="stream-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ stream.total_students }}</div>
                            <div class="stat-label">Total Students</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ stream.students_with_marks }}</div>
                            <div class="stat-label">With Marks</div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        {% if stream.can_generate_report %}
                            <a href="{{ url_for('classteacher.generate_individual_stream_report', 
                                              grade_name=grade, stream_name=stream.name, 
                                              term=term, assessment_type=assessment_type) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-file-pdf me-1"></i>Generate Report
                            </a>
                        {% else %}
                            <button class="btn btn-primary" disabled>
                                <i class="fas fa-file-pdf me-1"></i>Generate Report
                            </button>
                        {% endif %}
                        
                        <div class="form-check mt-2">
                            <input class="form-check-input stream-checkbox" type="checkbox" 
                                   value="{{ stream.name }}" id="stream_{{ stream.name }}"
                                   {% if stream.can_generate_report %}checked{% else %}disabled{% endif %}>
                            <label class="form-check-label" for="stream_{{ stream.name }}">
                                Include in batch
                            </label>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Grade-Level Actions -->
            {% if can_generate_reports %}
            <div class="grade-actions">
                <h4>
                    <i class="fas fa-chart-bar me-2"></i>
                    Grade-Level Report Options
                </h4>
                
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('classteacher.generate_consolidated_grade_report', 
                                          grade_name=grade, term=term, assessment_type=assessment_type) }}" 
                           class="btn btn-success btn-lg w-100 mb-3">
                            <i class="fas fa-chart-line me-2"></i>
                            Consolidated Grade Report
                        </a>
                    </div>
                    <div class="col-md-4">
                        <button onclick="generateBatchReports()" class="btn btn-warning btn-lg w-100 mb-3">
                            <i class="fas fa-download me-2"></i>
                            Batch Download (ZIP)
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button onclick="generateSelectedReports()" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-check-square me-2"></i>
                            Selected Streams Only
                        </button>
                    </div>
                </div>

                <div class="stream-selection">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include_individual" checked>
                        <label class="form-check-label" for="include_individual">
                            Include individual stream reports
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include_consolidated" checked>
                        <label class="form-check-label" for="include_consolidated">
                            Include consolidated grade report
                        </label>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>No reports available:</strong> Please ensure students have marks uploaded for this term and assessment type.
            </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function generateBatchReports() {
            const includeIndividual = document.getElementById('include_individual').checked;
            const includeConsolidated = document.getElementById('include_consolidated').checked;
            
            const url = `/classteacher/generate_batch_grade_reports/{{ grade }}/{{ term }}/{{ assessment_type }}?individual=${includeIndividual}&consolidated=${includeConsolidated}`;
            window.location.href = url;
        }

        function generateSelectedReports() {
            const selectedStreams = Array.from(document.querySelectorAll('.stream-checkbox:checked')).map(cb => cb.value);
            const includeIndividual = document.getElementById('include_individual').checked;
            const includeConsolidated = document.getElementById('include_consolidated').checked;
            
            if (selectedStreams.length === 0) {
                alert('Please select at least one stream.');
                return;
            }
            
            const streamParams = selectedStreams.map(s => `streams=${s}`).join('&');
            const url = `/classteacher/generate_batch_grade_reports/{{ grade }}/{{ term }}/{{ assessment_type }}?individual=${includeIndividual}&consolidated=${includeConsolidated}&${streamParams}`;
            window.location.href = url;
        }
    </script>
</body>
</html>
