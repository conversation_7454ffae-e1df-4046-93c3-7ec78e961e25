#!/usr/bin/env python3
"""
Test script to verify UI fixes for Recent Reports action buttons and Edit Marks form inputs
"""

import requests
from bs4 import BeautifulSoup
import re

def test_action_buttons_styling():
    """Test that action buttons have proper styling"""
    print("🔍 TESTING ACTION BUTTONS STYLING")
    print("=" * 50)
    
    try:
        # Read the all_reports.html template
        with open('templates/all_reports.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for improved action button CSS
        checks = [
            ('Enhanced action-btn class', 'display: inline-flex'),
            ('Gradient backgrounds', 'linear-gradient'),
            ('Hover effects', 'transform: translateY(-2px)'),
            ('Responsive design', '@media (max-width: 768px)'),
            ('Proper button labels', '<span>Edit</span>'),
            ('Tooltips added', 'title="Edit marks'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                results.append(f"✅ {check_name}: Found")
            else:
                results.append(f"❌ {check_name}: Missing")
        
        for result in results:
            print(f"   {result}")
            
        return all("✅" in result for result in results)
        
    except Exception as e:
        print(f"❌ Error testing action buttons: {e}")
        return False

def test_form_input_sizing():
    """Test that form inputs are properly sized"""
    print("\n🔍 TESTING FORM INPUT SIZING")
    print("=" * 50)
    
    try:
        # Read the edit_class_marks.html template
        with open('templates/edit_class_marks.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for improved input sizing
        checks = [
            ('Proper input width', 'width: 70px !important'),
            ('Proper input height', 'height: 32px !important'),
            ('Professional padding', 'padding: 4px 8px !important'),
            ('Clean borders', 'border: 1px solid #ced4da'),
            ('Subject input sizing', 'width: 80px'),
            ('Responsive design', '@media (max-width: 768px)'),
            ('Student name column styling', 'td:first-child'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                results.append(f"✅ {check_name}: Found")
            else:
                results.append(f"❌ {check_name}: Missing")
        
        for result in results:
            print(f"   {result}")
            
        return all("✅" in result for result in results)
        
    except Exception as e:
        print(f"❌ Error testing form inputs: {e}")
        return False

def test_responsive_design():
    """Test responsive design elements"""
    print("\n🔍 TESTING RESPONSIVE DESIGN")
    print("=" * 50)
    
    try:
        # Check both files for responsive design
        files_to_check = [
            ('templates/all_reports.html', 'Action Buttons'),
            ('templates/edit_class_marks.html', 'Form Inputs')
        ]
        
        results = []
        for file_path, component in files_to_check:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for responsive breakpoints
            breakpoints = ['@media (max-width: 768px)', '@media (max-width: 480px)']
            responsive_found = any(bp in content for bp in breakpoints)
            
            if responsive_found:
                results.append(f"✅ {component}: Responsive design implemented")
            else:
                results.append(f"❌ {component}: No responsive design found")
        
        for result in results:
            print(f"   {result}")
            
        return all("✅" in result for result in results)
        
    except Exception as e:
        print(f"❌ Error testing responsive design: {e}")
        return False

def test_dynamic_school_name():
    """Test that school names are dynamic instead of hardcoded"""
    print("\n🔍 TESTING DYNAMIC SCHOOL NAME")
    print("=" * 50)

    try:
        files_to_check = [
            ('templates/all_reports.html', 'Recent Reports'),
            ('templates/edit_class_marks.html', 'Edit Marks')
        ]

        results = []
        for file_path, component in files_to_check:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check for dynamic school name usage
            if 'school_info.school_name' in content:
                results.append(f"✅ {component}: Dynamic school name implemented")
            else:
                results.append(f"❌ {component}: Still using hardcoded school name")

            # Check for hardcoded names
            hardcoded_names = ['KIRIMA PRIMARY SCHOOL', 'Kirima Primary School']
            has_hardcoded = any(name in content for name in hardcoded_names)

            if not has_hardcoded:
                results.append(f"✅ {component}: No hardcoded school names found")
            else:
                results.append(f"❌ {component}: Still contains hardcoded school names")

        for result in results:
            print(f"   {result}")

        return all("✅" in result for result in results)

    except Exception as e:
        print(f"❌ Error testing dynamic school names: {e}")
        return False

def main():
    """Run all UI fix tests"""
    print("🚀 HILLVIEW UI FIXES VERIFICATION")
    print("=" * 60)

    # Run all tests
    test1 = test_action_buttons_styling()
    test2 = test_form_input_sizing()
    test3 = test_responsive_design()
    test4 = test_dynamic_school_name()

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    if test1:
        print("✅ Action Buttons Styling: PASSED")
    else:
        print("❌ Action Buttons Styling: FAILED")

    if test2:
        print("✅ Form Input Sizing: PASSED")
    else:
        print("❌ Form Input Sizing: FAILED")

    if test3:
        print("✅ Responsive Design: PASSED")
    else:
        print("❌ Responsive Design: FAILED")

    if test4:
        print("✅ Dynamic School Name: PASSED")
    else:
        print("❌ Dynamic School Name: FAILED")

    overall_success = test1 and test2 and test3 and test4

    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 ALL UI FIXES SUCCESSFULLY IMPLEMENTED!")
        print("\n📋 MANUAL TESTING STEPS:")
        print("1. Start the application: python run.py")
        print("2. Clear browser cache (Ctrl+Shift+R or Ctrl+F5)")
        print("3. Navigate to Recent Reports page")
        print("4. Verify action buttons are clearly distinguishable with gradients")
        print("5. Navigate to Edit Class Report Marks")
        print("6. Verify form inputs are properly sized (70px width)")
        print("7. Check that school name displays dynamically")
        print("8. Test on mobile devices for responsive design")
        print("\n⚠️  IMPORTANT: Clear browser cache to see changes!")
    else:
        print("⚠️  SOME FIXES NEED ATTENTION - Check failed tests above")

    return overall_success

if __name__ == "__main__":
    main()
