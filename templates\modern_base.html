<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}{{ school_info.school_name or 'Hillview School' }}
      Management{% endblock %}
    </title>

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Modern CSS Framework -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/modern_classteacher.css') }}"
    />

    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}

    <style>
      /* Page-specific modern enhancements */
      .page-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: var(--space-6);
      }

      .content-wrapper {
        max-width: 1400px;
        margin: 0 auto;
      }

      .page-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: var(--space-8);
        margin-bottom: var(--space-8);
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .breadcrumb-nav {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-xl);
        padding: var(--space-4) var(--space-6);
        margin-bottom: var(--space-6);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .breadcrumb-nav a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .breadcrumb-nav a:hover {
        color: white;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
      }

      .breadcrumb-nav .separator {
        color: rgba(255, 255, 255, 0.6);
        margin: 0 var(--space-3);
      }

      .breadcrumb-nav .current {
        color: white;
        font-weight: 600;
      }

      .main-content {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: var(--space-8);
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.2);
        min-height: 60vh;
      }

      .flash-messages {
        margin-bottom: var(--space-6);
      }

      .flash-message {
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-3);
        border: 1px solid;
        display: flex;
        align-items: center;
        gap: var(--space-3);
        font-weight: 500;
        animation: slideUp 0.3s ease-out;
      }

      .flash-success {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.2);
        color: rgb(21, 128, 61);
      }

      .flash-warning {
        background: rgba(245, 158, 11, 0.1);
        border-color: rgba(245, 158, 11, 0.2);
        color: rgb(146, 64, 14);
      }

      .flash-error {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.2);
        color: rgb(153, 27, 27);
      }

      .flash-info {
        background: rgba(59, 130, 246, 0.1);
        border-color: rgba(59, 130, 246, 0.2);
        color: rgb(30, 64, 175);
      }

      .page-actions {
        display: flex;
        gap: var(--space-4);
        margin-bottom: var(--space-6);
        flex-wrap: wrap;
      }

      .back-button {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-3) var(--space-6);
        background: rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
        border-radius: var(--radius-lg);
        font-weight: 600;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .back-button:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        text-decoration: none;
        color: white;
      }

      /* Mobile-specific enhancements */
      @media (max-width: 768px) {
        .page-container {
          padding: var(--space-3);
        }

        .content-wrapper {
          padding: 0;
        }

        .page-header {
          padding: var(--space-4);
          margin-bottom: var(--space-4);
        }

        .header-content {
          flex-direction: column;
          text-align: center;
          gap: var(--space-4);
        }

        .header-title {
          font-size: 1.5rem;
        }

        .header-subtitle {
          font-size: 0.875rem;
        }

        .header-actions {
          width: 100%;
          justify-content: center;
        }

        .back-button {
          width: 100%;
          justify-content: center;
        }

        .main-content {
          padding: var(--space-4);
        }

        .breadcrumb-nav {
          padding: var(--space-3);
          margin-bottom: var(--space-4);
        }

        .breadcrumb-nav a,
        .breadcrumb-nav .current {
          font-size: 0.875rem;
        }
      }

      @media (max-width: 480px) {
        .page-container {
          padding: var(--space-2);
        }

        .page-header {
          padding: var(--space-3);
          margin-bottom: var(--space-3);
        }

        .main-content {
          padding: var(--space-3);
        }

        .header-title {
          font-size: 1.25rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <div class="content-wrapper">
        <!-- Breadcrumb Navigation -->
        {% block breadcrumb %}
        <nav class="breadcrumb-nav">
          <a href="{{ url_for('classteacher.dashboard') }}">
            <i class="fas fa-home"></i>
            Dashboard
          </a>
          <span class="separator">
            <i class="fas fa-chevron-right"></i>
          </span>
          <span class="current">{% block page_title %}Page{% endblock %}</span>
        </nav>
        {% endblock %}

        <!-- Page Header -->
        <header class="page-header fade-in">
          <div class="header-content">
            <div>
              <h1 class="header-title">
                {% block header_icon %}<i class="fas fa-cog"></i>{% endblock %}
                {% block header_title %}Page Title{% endblock %}
              </h1>
              <p class="header-subtitle">
                {% block header_subtitle %}Page description{% endblock %}
              </p>
            </div>
            <div class="header-actions">
              {% block header_actions %}
              <a
                href="{{ url_for('classteacher.dashboard') }}"
                class="back-button"
              >
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
              </a>
              {% endblock %}
            </div>
          </div>
        </header>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %}
        <div class="flash-messages">
          {% for category, message in messages %}
          <div
            class="flash-message flash-{{ 'error' if category == 'error' else category }}"
          >
            <i
              class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' if category == 'warning' else 'times-circle' if category == 'error' else 'info-circle' }}"
            ></i>
            {{ message }}
          </div>
          {% endfor %}
        </div>
        {% endif %} {% endwith %}

        <!-- Main Content -->
        <main class="main-content slide-up">
          {% block content %}
          <p>Content goes here</p>
          {% endblock %}
        </main>
      </div>
    </div>

    <!-- JavaScript -->
    <script>
      // Modern UI JavaScript
      document.addEventListener("DOMContentLoaded", function () {
        // Auto-hide flash messages after 5 seconds
        const flashMessages = document.querySelectorAll(".flash-message");
        flashMessages.forEach((message) => {
          setTimeout(() => {
            message.style.animation = "fadeOut 0.3s ease-out forwards";
            setTimeout(() => {
              message.remove();
            }, 300);
          }, 5000);
        });

        // Add loading states to form submissions
        const forms = document.querySelectorAll("form");
        forms.forEach((form) => {
          form.addEventListener("submit", function () {
            const submitButton = form.querySelector(
              'button[type="submit"], input[type="submit"]'
            );
            if (submitButton) {
              submitButton.classList.add("loading");
              submitButton.disabled = true;
            }
          });
        });

        // Enhanced button interactions
        const buttons = document.querySelectorAll(".modern-btn");
        buttons.forEach((button) => {
          button.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-2px)";
          });

          button.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0)";
          });
        });
      });

      // Add fadeOut animation
      const style = document.createElement("style");
      style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-10px); }
            }
        `;
      document.head.appendChild(style);
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
