#!/usr/bin/env python3
"""
Test auth blueprint import in isolation.
"""

import os
import sys

# Add the parent directory to the Python path so we can import new_structure as a package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

print("🔍 Auth blueprint isolation test...")

try:
    print("Step 1: Test services import...")
    from new_structure.services import authenticate_teacher, logout
    print("✅ Services imported")
    
    print("Step 2: Test direct auth module import...")
    import new_structure.views.auth as auth_module
    print("✅ Auth module imported")

    print("Step 3: Get auth blueprint...")
    auth_bp = auth_module.auth_bp
    print("✅ Auth blueprint obtained")
    
    print("🎯 Auth test passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
