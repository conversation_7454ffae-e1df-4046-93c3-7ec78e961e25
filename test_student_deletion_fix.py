#!/usr/bin/env python3
"""
Test script to verify student deletion fix works
"""

from __init__ import create_app
from models.academic import Student, <PERSON>, ComponentMark
from services.student_service import delete_student
from extensions import db

def test_student_deletion():
    """Test student deletion with parent portal disabled"""
    app = create_app()
    
    with app.app_context():
        print("🧪 Testing Student Deletion Fix")
        print("=" * 50)
        
        # Get a student to test with
        students = Student.query.limit(3).all()
        
        if not students:
            print("❌ No students found in database")
            return
        
        print(f"📊 Found {len(students)} students:")
        for student in students:
            print(f"  - ID: {student.id}, Name: {student.name}")
        
        # Test deletion with the first student
        test_student = students[0]
        print(f"\n🎯 Testing deletion of student: {test_student.name} (ID: {test_student.id})")
        
        # Check if student has marks
        marks = Mark.query.filter_by(student_id=test_student.id).all()
        print(f"📚 Student has {len(marks)} marks")
        
        # Check if student has component marks
        component_marks = []
        for mark in marks:
            components = ComponentMark.query.filter_by(mark_id=mark.id).all()
            component_marks.extend(components)
        print(f"📝 Student has {len(component_marks)} component marks")
        
        # Test the deletion function
        print(f"\n🔧 Testing delete_student() function...")
        try:
            result = delete_student(test_student.id)
            
            if result['success']:
                print(f"✅ SUCCESS: {result['message']}")
                
                # Verify student is actually deleted
                deleted_student = Student.query.get(test_student.id)
                if deleted_student is None:
                    print("✅ VERIFIED: Student successfully removed from database")
                else:
                    print("❌ ERROR: Student still exists in database")
                
                # Verify marks are deleted
                remaining_marks = Mark.query.filter_by(student_id=test_student.id).all()
                if len(remaining_marks) == 0:
                    print("✅ VERIFIED: All marks successfully deleted")
                else:
                    print(f"❌ ERROR: {len(remaining_marks)} marks still exist")
                
                # Verify component marks are deleted
                remaining_components = []
                for mark in marks:
                    components = ComponentMark.query.filter_by(mark_id=mark.id).all()
                    remaining_components.extend(components)
                if len(remaining_components) == 0:
                    print("✅ VERIFIED: All component marks successfully deleted")
                else:
                    print(f"❌ ERROR: {len(remaining_components)} component marks still exist")
                    
            else:
                print(f"❌ FAILED: {result['message']}")
                
        except Exception as e:
            print(f"❌ EXCEPTION: {str(e)}")
        
        print("\n" + "=" * 50)
        print("🏁 Student Deletion Test Complete")

if __name__ == "__main__":
    test_student_deletion()
