#!/usr/bin/env python3
"""
Comprehensive Functional Tests for Student Promotion System
==========================================================

This test suite validates the functional aspects of the student promotion system:
1. Database migration testing
2. Bulk promotion functionality
3. Individual promotion operations
4. History tracking
5. Statistics generation
6. Integration with existing school management features
7. Performance testing with large datasets
"""

import pytest
import json
import time
from datetime import datetime
from unittest.mock import patch, MagicMock
import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db, Student, Teacher, Grade, Stream, StudentPromotionHistory
from services.student_promotion_service import StudentPromotionService


class TestStudentPromotionFunctionality:
    """Test functional aspects of student promotion system."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        from __init__ import create_app
        app = create_app('testing')
        
        with app.app_context():
            db.create_all()
            self.setup_test_data()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def headteacher_session(self, client):
        """Create authenticated headteacher session."""
        with client.session_transaction() as sess:
            sess['teacher_id'] = 1
            sess['role'] = 'headteacher'
            sess['authenticated'] = True
            sess['csrf_token'] = 'test-csrf-token'
        return client
    
    def setup_test_data(self):
        """Set up test data for promotion testing."""
        # Create grades
        grades = []
        for i in range(1, 10):
            grade = Grade(name=f'Grade {i}')
            db.session.add(grade)
            grades.append(grade)
        
        # Create streams
        streams = []
        for grade in grades:
            stream = Stream(name='A', grade=grade)
            db.session.add(stream)
            streams.append(stream)
        
        # Create headteacher
        headteacher = Teacher(
            name='Head Teacher',
            email='<EMAIL>',
            role='headteacher'
        )
        db.session.add(headteacher)
        
        # Create students
        students = []
        for i in range(1, 21):  # 20 test students
            grade_index = (i - 1) % 8  # Distribute across grades 1-8
            student = Student(
                name=f'Student {i}',
                admission_number=f'ADM{i:03d}',
                grade=grades[grade_index],
                stream=streams[grade_index],
                academic_year='2024',
                promotion_status='active',
                is_eligible_for_promotion=True
            )
            db.session.add(student)
            students.append(student)
        
        db.session.commit()
        return grades, streams, students, headteacher
    
    def test_database_migration_fields(self, app):
        """Test that all promotion-related database fields exist."""
        with app.app_context():
            # Check Student model has promotion fields
            student = Student.query.first()
            assert hasattr(student, 'promotion_status')
            assert hasattr(student, 'academic_year')
            assert hasattr(student, 'date_last_promoted')
            assert hasattr(student, 'is_eligible_for_promotion')
            assert hasattr(student, 'promotion_notes')
            
            # Check StudentPromotionHistory table exists
            history_count = StudentPromotionHistory.query.count()
            assert history_count >= 0  # Table exists and is queryable
    
    def test_promotion_preview_data_generation(self, app):
        """Test promotion preview data generation."""
        with app.app_context():
            preview_data = StudentPromotionService.get_promotion_preview_data()
            
            assert 'statistics' in preview_data
            assert 'grades' in preview_data
            assert 'current_academic_year' in preview_data
            
            # Check statistics structure
            stats = preview_data['statistics']
            assert 'total_students' in stats
            assert 'eligible_for_promotion' in stats
            assert 'by_grade' in stats
            
            # Check grades data
            grades = preview_data['grades']
            assert isinstance(grades, list)
            assert len(grades) > 0
            
            # Each grade should have students
            for grade in grades:
                assert 'grade_name' in grade
                assert 'students' in grade
                assert isinstance(grade['students'], list)
    
    def test_individual_student_promotion(self, app):
        """Test individual student promotion operations."""
        with app.app_context():
            student = Student.query.first()
            original_grade_id = student.grade_id
            
            # Test promotion
            promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': student.id,
                        'action': 'promote',
                        'to_grade_id': original_grade_id + 1,
                        'to_stream_id': student.stream_id,
                        'notes': 'Test promotion'
                    }
                ]
            }
            
            result = StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            assert result['success'] is True
            assert result['processed_count'] == 1
            assert result['promoted_count'] == 1
            
            # Verify student was promoted
            updated_student = Student.query.get(student.id)
            assert updated_student.grade_id == original_grade_id + 1
            assert updated_student.promotion_status == 'promoted'
            assert updated_student.academic_year == '2025'
            
            # Verify history record was created
            history = StudentPromotionHistory.query.filter_by(student_id=student.id).first()
            assert history is not None
            assert history.promotion_type == 'promote'
            assert history.academic_year_to == '2025'
    
    def test_student_repeat_operation(self, app):
        """Test student repeat operation."""
        with app.app_context():
            student = Student.query.first()
            original_grade_id = student.grade_id
            
            promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': student.id,
                        'action': 'repeat',
                        'notes': 'Needs improvement'
                    }
                ]
            }
            
            result = StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            assert result['success'] is True
            assert result['repeated_count'] == 1
            
            # Verify student status
            updated_student = Student.query.get(student.id)
            assert updated_student.grade_id == original_grade_id  # Same grade
            assert updated_student.promotion_status == 'repeated'
            assert updated_student.academic_year == '2025'
    
    def test_student_graduation(self, app):
        """Test student graduation from Grade 9."""
        with app.app_context():
            # Find or create a Grade 9 student
            grade_9 = Grade.query.filter_by(name='Grade 9').first()
            student = Student.query.filter_by(grade_id=grade_9.id).first()
            
            if not student:
                # Create a Grade 9 student for testing
                stream_9 = Stream.query.filter_by(grade_id=grade_9.id).first()
                student = Student(
                    name='Grade 9 Student',
                    admission_number='ADM999',
                    grade=grade_9,
                    stream=stream_9,
                    academic_year='2024',
                    promotion_status='active'
                )
                db.session.add(student)
                db.session.commit()
            
            promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': student.id,
                        'action': 'graduate',
                        'notes': 'Completed Grade 9'
                    }
                ]
            }
            
            result = StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            assert result['success'] is True
            assert result['graduated_count'] == 1
            
            # Verify student graduated
            updated_student = Student.query.get(student.id)
            assert updated_student.promotion_status == 'graduated'
    
    def test_bulk_promotion_processing(self, app):
        """Test bulk promotion of multiple students."""
        with app.app_context():
            students = Student.query.limit(5).all()
            
            promotion_data = {
                'academic_year_to': '2025',
                'students': []
            }
            
            for student in students:
                promotion_data['students'].append({
                    'student_id': student.id,
                    'action': 'promote',
                    'to_grade_id': student.grade_id + 1 if student.grade_id < 9 else student.grade_id,
                    'to_stream_id': student.stream_id,
                    'notes': f'Bulk promotion for {student.name}'
                })
            
            result = StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            assert result['success'] is True
            assert result['processed_count'] == 5
            assert result['promoted_count'] == 5
            
            # Verify all students were promoted
            for student in students:
                updated_student = Student.query.get(student.id)
                assert updated_student.promotion_status == 'promoted'
                assert updated_student.academic_year == '2025'
    
    def test_promotion_history_tracking(self, app):
        """Test that promotion history is properly tracked."""
        with app.app_context():
            student = Student.query.first()
            
            # Perform multiple promotions
            for year in ['2025', '2026']:
                promotion_data = {
                    'academic_year_to': year,
                    'students': [
                        {
                            'student_id': student.id,
                            'action': 'promote',
                            'to_grade_id': student.grade_id + 1,
                            'to_stream_id': student.stream_id,
                            'notes': f'Promotion to {year}'
                        }
                    ]
                }
                
                StudentPromotionService.process_bulk_promotion(
                    promotion_data, 
                    teacher_id=1
                )
                
                # Update student for next iteration
                student = Student.query.get(student.id)
            
            # Check history records
            history_records = StudentPromotionService.get_promotion_history(
                student_id=student.id
            )
            
            assert len(history_records) >= 2
            
            # Verify history details
            for record in history_records:
                assert record['student_name'] == student.name
                assert record['promotion_type'] == 'promote'
                assert record['academic_year_to'] in ['2025', '2026']
    
    def test_promotion_statistics_generation(self, app):
        """Test promotion statistics generation."""
        with app.app_context():
            # Perform some promotions first
            students = Student.query.limit(3).all()
            
            promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': students[0].id,
                        'action': 'promote',
                        'to_grade_id': students[0].grade_id + 1,
                        'to_stream_id': students[0].stream_id
                    },
                    {
                        'student_id': students[1].id,
                        'action': 'repeat'
                    },
                    {
                        'student_id': students[2].id,
                        'action': 'transfer'
                    }
                ]
            }
            
            StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            # Get statistics
            stats = StudentPromotionService.get_promotion_statistics('2025')
            
            assert 'total_promotions' in stats
            assert 'by_type' in stats
            assert 'by_grade' in stats
            assert 'recent_activity' in stats
            
            # Verify promotion counts
            by_type = stats['by_type']
            assert by_type['promote'] >= 1
            assert by_type['repeat'] >= 1
            assert by_type['transfer'] >= 1
    
    def test_performance_with_large_dataset(self, app):
        """Test system performance with larger datasets."""
        with app.app_context():
            # This test would be expanded for actual performance testing
            start_time = time.time()
            
            # Get preview data (simulates loading page with many students)
            preview_data = StudentPromotionService.get_promotion_preview_data()
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should complete within reasonable time (adjust threshold as needed)
            assert processing_time < 5.0  # 5 seconds max
            
            # Verify data structure is complete
            assert 'statistics' in preview_data
            assert 'grades' in preview_data
            assert len(preview_data['grades']) > 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
