<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMTP Settings - Hillview School</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .smtp-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
        }
        
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        
        .form-body {
            background: white;
            padding: 30px;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .info-box h4 {
            color: #1976d2;
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-box p {
            margin: 0 0 10px 0;
            color: #424242;
            line-height: 1.5;
        }
        
        .info-box ul {
            margin: 10px 0 0 20px;
            color: #424242;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-control[type="password"] {
            font-family: monospace;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .required {
            color: #e74c3c;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .provider-examples {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .provider-examples h5 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .provider-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .provider-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
        }
        
        .provider-name {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .provider-settings {
            font-size: 0.9em;
            color: #666;
            font-family: monospace;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .warning-box h5 {
            color: #856404;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .warning-box p {
            margin: 0;
            color: #856404;
        }
        
        .env-config {
            background: #f8f9fa;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-line;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="smtp-container">
        <div class="form-header">
            <h1><i class="fas fa-server"></i> SMTP Configuration</h1>
            <p>Configure email server settings for parent notifications</p>
        </div>
        
        <div class="form-body">
            <div class="info-box">
                <h4><i class="fas fa-info-circle"></i> Before You Start</h4>
                <p>To send emails to parents, you need to configure SMTP settings. Here's what you'll need:</p>
                <ul>
                    <li>SMTP server address (e.g., smtp.gmail.com)</li>
                    <li>SMTP port number (usually 587 for TLS or 465 for SSL)</li>
                    <li>Email account username and password</li>
                    <li>For Gmail: You'll need to use an "App Password" instead of your regular password</li>
                </ul>
            </div>
            
            <div class="warning-box">
                <h5><i class="fas fa-exclamation-triangle"></i> Important Security Note</h5>
                <p>This form generates environment variables that you need to set manually. Never store email passwords directly in your code or database.</p>
            </div>
            
            <form method="POST" id="smtpForm">
                <div class="form-group">
                    <label for="smtp_server" class="form-label">
                        SMTP Server <span class="required">*</span>
                    </label>
                    <input type="text" 
                           id="smtp_server" 
                           name="smtp_server" 
                           class="form-control" 
                           value="{{ config.smtp_server }}"
                           placeholder="smtp.gmail.com"
                           required>
                </div>
                
                <div class="form-group">
                    <label for="smtp_port" class="form-label">
                        SMTP Port <span class="required">*</span>
                    </label>
                    <input type="number" 
                           id="smtp_port" 
                           name="smtp_port" 
                           class="form-control" 
                           value="{{ config.smtp_port }}"
                           placeholder="587"
                           min="1" 
                           max="65535"
                           required>
                </div>
                
                <div class="form-group">
                    <label for="smtp_username" class="form-label">
                        Email Username <span class="required">*</span>
                    </label>
                    <input type="email" 
                           id="smtp_username" 
                           name="smtp_username" 
                           class="form-control" 
                           value="{{ config.smtp_username }}"
                           placeholder="<EMAIL>"
                           required>
                </div>
                
                <div class="form-group">
                    <label for="smtp_password" class="form-label">
                        Email Password <span class="required">*</span>
                    </label>
                    <input type="password" 
                           id="smtp_password" 
                           name="smtp_password" 
                           class="form-control" 
                           placeholder="Enter your email password or app password"
                           required>
                    <small style="color: #666; font-size: 0.9em; margin-top: 5px; display: block;">
                        For Gmail, use an App Password instead of your regular password
                    </small>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="use_tls" name="use_tls" {{ 'checked' if config.use_tls else '' }}>
                    <label for="use_tls">Use TLS encryption (recommended)</label>
                </div>
                
                <div style="margin-top: 30px;">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Generate Configuration
                    </button>
                </div>
            </form>
            
            <!-- Provider Examples -->
            <div class="provider-examples">
                <h5>Common Email Provider Settings</h5>
                <div class="provider-list">
                    <div class="provider-item">
                        <div class="provider-name">Gmail</div>
                        <div class="provider-settings">
                            Server: smtp.gmail.com<br>
                            Port: 587 (TLS)<br>
                            Note: Use App Password
                        </div>
                    </div>
                    <div class="provider-item">
                        <div class="provider-name">Outlook/Hotmail</div>
                        <div class="provider-settings">
                            Server: smtp-mail.outlook.com<br>
                            Port: 587 (TLS)
                        </div>
                    </div>
                    <div class="provider-item">
                        <div class="provider-name">Yahoo Mail</div>
                        <div class="provider-settings">
                            Server: smtp.mail.yahoo.com<br>
                            Port: 587 (TLS)
                        </div>
                    </div>
                    <div class="provider-item">
                        <div class="provider-name">Custom SMTP</div>
                        <div class="provider-settings">
                            Contact your email provider<br>
                            for specific settings
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Environment Configuration Display -->
            {% if session.env_config %}
            <div style="margin-top: 30px;">
                <h5>Environment Configuration</h5>
                <p>Copy these environment variables and add them to your system:</p>
                <div class="env-config">{{ session.env_config }}</div>
                <p style="color: #666; font-size: 0.9em; margin-top: 10px;">
                    After setting these variables, restart the application for changes to take effect.
                </p>
            </div>
            {% endif %}
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="{{ url_for('email_config.dashboard') }}" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Email Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}" 
                         style="margin-bottom: 10px; padding: 15px; border-radius: 8px; background: white; box-shadow: 0 4px 15px rgba(0,0,0,0.1); max-width: 400px;">
                        {{ message }}
                        <button type="button" class="close" onclick="this.parentElement.remove();" 
                                style="float: right; background: none; border: none; font-size: 1.2em; cursor: pointer;">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <script>
        // Auto-hide flash messages
        setTimeout(function() {
            const flashMessages = document.querySelector('.flash-messages');
            if (flashMessages) {
                flashMessages.style.opacity = '0';
                flashMessages.style.transition = 'opacity 0.5s ease';
                setTimeout(() => flashMessages.remove(), 500);
            }
        }, 5000);
        
        // Clear session env_config after displaying
        {% if session.env_config %}
        fetch('{{ url_for("email_config.dashboard") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({action: 'clear_env_config'})
        });
        {% endif %}
    </script>
</body>
</html>
