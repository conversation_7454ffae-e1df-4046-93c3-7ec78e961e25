"""
Comprehensive Unit Tests for Headteacher Functionality
Following Keploy principles: Build, Pass, Coverage, Clean tests

This test suite covers ALL headteacher features:
1. Authentication & Session Management
2. Dashboard & Analytics
3. Universal Access Functions
4. Staff Management
5. Reports & Analytics
6. Parent Management
7. Permissions Management
8. Subject Configuration
"""

import unittest
import json
import sys
import os
from unittest.mock import patch, MagicMock

# Add the parent directory to the path to import the app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import session
from extensions import db
from models.user import Teacher
from models.academic import Student, Grade, Stream, Subject
from models.school_config import SchoolConfiguration


class HeadteacherComprehensiveTestSuite(unittest.TestCase):
    """Comprehensive test suite for all headteacher functionality."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment once for all tests."""
        try:
            from config import config
            from __init__ import create_app
            
            # Create test app with testing configuration
            cls.app = create_app(config['testing']())
            cls.app_context = cls.app.app_context()
            cls.app_context.push()
            cls.client = cls.app.test_client()
            
            # Create test database
            db.create_all()
            
            # Create test headteacher
            cls.test_headteacher = Teacher(
                username='test_headteacher',
                password='test_password',
                role='headteacher',
                first_name='Test',
                last_name='Headteacher',
                is_active=True
            )
            db.session.add(cls.test_headteacher)
            db.session.commit()
            
            print("✅ Test environment set up successfully")
            
        except Exception as e:
            print(f"❌ Error setting up test environment: {e}")
            raise
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        try:
            db.session.remove()
            db.drop_all()
            cls.app_context.pop()
            print("✅ Test environment cleaned up")
        except Exception as e:
            print(f"⚠️ Error cleaning up test environment: {e}")
    
    def setUp(self):
        """Set up for each test."""
        self.client = self.app.test_client()
        
    def tearDown(self):
        """Clean up after each test."""
        # Clear session
        with self.client.session_transaction() as sess:
            sess.clear()
    
    # ============================================================================
    # AUTHENTICATION & SESSION MANAGEMENT TESTS
    # ============================================================================
    
    def test_01_headteacher_login_success(self):
        """Test successful headteacher login."""
        print("\n🧪 Testing: Headteacher Login Success")
        
        response = self.client.post('/admin_login', data={
            'username': 'test_headteacher',
            'password': 'test_password'
        }, follow_redirects=False)
        
        # Should redirect to dashboard on success
        self.assertEqual(response.status_code, 302)
        self.assertIn('/headteacher', response.location)
        
        print("✅ Headteacher login successful - redirects to dashboard")
    
    def test_02_headteacher_login_invalid_credentials(self):
        """Test headteacher login with invalid credentials."""
        print("\n🧪 Testing: Headteacher Login Invalid Credentials")
        
        response = self.client.post('/admin_login', data={
            'username': 'wrong_user',
            'password': 'wrong_password'
        })
        
        # Should return login page with error
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Invalid credentials', response.data)
        
        print("✅ Invalid credentials properly rejected")
    
    def test_03_headteacher_session_management(self):
        """Test headteacher session is properly managed."""
        print("\n🧪 Testing: Headteacher Session Management")
        
        # Login first
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'
            sess.permanent = True
        
        # Test session persistence
        response = self.client.get('/headteacher/')
        self.assertEqual(response.status_code, 200)
        
        print("✅ Headteacher session properly managed")
    
    def test_04_unauthorized_access_protection(self):
        """Test that headteacher routes are protected from unauthorized access."""
        print("\n🧪 Testing: Unauthorized Access Protection")
        
        # Try to access headteacher dashboard without login
        response = self.client.get('/headteacher/', follow_redirects=False)
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn('/admin_login', response.location)
        
        print("✅ Unauthorized access properly blocked")
    
    # ============================================================================
    # DASHBOARD & ANALYTICS TESTS
    # ============================================================================
    
    def test_05_headteacher_dashboard_loads(self):
        """Test headteacher dashboard loads with proper data."""
        print("\n🧪 Testing: Headteacher Dashboard Loading")
        
        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'
        
        response = self.client.get('/headteacher/')
        self.assertEqual(response.status_code, 200)
        
        # Check for key dashboard elements
        self.assertIn(b'Dashboard', response.data)
        self.assertIn(b'Total Students', response.data)
        self.assertIn(b'Total Teachers', response.data)
        
        print("✅ Headteacher dashboard loads successfully")
    
    def test_06_analytics_dashboard_access(self):
        """Test analytics dashboard is accessible to headteacher."""
        print("\n🧪 Testing: Analytics Dashboard Access")
        
        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'
        
        response = self.client.get('/headteacher/analytics')
        self.assertEqual(response.status_code, 200)
        
        print("✅ Analytics dashboard accessible")
    
    def test_07_reports_page_access(self):
        """Test reports page is accessible to headteacher."""
        print("\n🧪 Testing: Reports Page Access")
        
        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'
        
        response = self.client.get('/headteacher/reports')
        self.assertEqual(response.status_code, 200)
        
        print("✅ Reports page accessible")

    # ============================================================================
    # UNIVERSAL ACCESS TESTS
    # ============================================================================

    def test_08_universal_access_dashboard(self):
        """Test headteacher universal access dashboard."""
        print("\n🧪 Testing: Universal Access Dashboard")

        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        response = self.client.get('/universal/dashboard')
        self.assertEqual(response.status_code, 200)

        # Check for universal access elements
        self.assertIn(b'Universal Access', response.data)
        self.assertIn(b'Management Hub', response.data)

        print("✅ Universal access dashboard loads")

    def test_09_universal_proxy_routes(self):
        """Test headteacher can access classteacher functions via proxy."""
        print("\n🧪 Testing: Universal Proxy Routes")

        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        # Test proxy routes
        proxy_routes = [
            '/universal/proxy/manage_students',
            '/universal/proxy/manage_teachers',
            '/universal/proxy/teacher_management_hub'
        ]

        for route in proxy_routes:
            response = self.client.get(route, follow_redirects=False)
            # Should redirect to actual classteacher route
            self.assertEqual(response.status_code, 302)
            print(f"✅ Proxy route {route} working")

    # ============================================================================
    # STAFF MANAGEMENT TESTS
    # ============================================================================

    def test_10_staff_management_access(self):
        """Test headteacher can access staff management."""
        print("\n🧪 Testing: Staff Management Access")

        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        response = self.client.get('/headteacher/manage_teachers')
        self.assertEqual(response.status_code, 200)

        print("✅ Staff management accessible")

    # ============================================================================
    # PARENT MANAGEMENT TESTS
    # ============================================================================

    def test_11_parent_management_access(self):
        """Test headteacher can access parent management."""
        print("\n🧪 Testing: Parent Management Access")

        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        response = self.client.get('/parent_management/dashboard')
        self.assertEqual(response.status_code, 200)

        print("✅ Parent management accessible")

    # ============================================================================
    # PERMISSIONS MANAGEMENT TESTS
    # ============================================================================

    def test_12_permissions_management_access(self):
        """Test headteacher can access permissions management."""
        print("\n🧪 Testing: Permissions Management Access")

        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        response = self.client.get('/permission/manage_permissions')
        self.assertEqual(response.status_code, 200)

        print("✅ Permissions management accessible")

    # ============================================================================
    # SUBJECT CONFIGURATION TESTS
    # ============================================================================

    def test_13_subject_configuration_access(self):
        """Test headteacher can access subject configuration."""
        print("\n🧪 Testing: Subject Configuration Access")

        # Login as headteacher
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        response = self.client.get('/subject_config/configuration')
        # May redirect or load directly
        self.assertIn(response.status_code, [200, 302])

        print("✅ Subject configuration accessible")

    # ============================================================================
    # LOGOUT FUNCTIONALITY TESTS
    # ============================================================================

    def test_14_headteacher_logout(self):
        """Test headteacher logout functionality."""
        print("\n🧪 Testing: Headteacher Logout")

        # Login first
        with self.client.session_transaction() as sess:
            sess['teacher_id'] = self.test_headteacher.id
            sess['role'] = 'headteacher'

        # Logout
        response = self.client.get('/logout', follow_redirects=False)

        # Should redirect to login page
        self.assertEqual(response.status_code, 302)

        print("✅ Headteacher logout working")


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)
