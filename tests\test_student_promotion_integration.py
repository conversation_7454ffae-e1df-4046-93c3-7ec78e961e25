#!/usr/bin/env python3
"""
Integration Tests for Student Promotion System
==============================================

This test suite validates the integration of the student promotion system
with the existing Hillview School Management System:
1. Integration with existing school management features
2. UI/UX testing across different devices (simulated)
3. End-to-end workflow testing
4. Database consistency checks
5. Error handling and recovery
"""

import pytest
import json
import time
from datetime import datetime
from unittest.mock import patch, MagicMock
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db, Student, Teacher, Grade, Stream, StudentPromotionHistory
from services.student_promotion_service import StudentPromotionService


class TestStudentPromotionIntegration:
    """Test integration aspects of student promotion system."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        from __init__ import create_app
        app = create_app('testing')
        
        with app.app_context():
            db.create_all()
            self.setup_comprehensive_test_data()
            yield app
            db.drop_all()
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def headteacher_session(self, client):
        """Create authenticated headteacher session."""
        with client.session_transaction() as sess:
            sess['teacher_id'] = 1
            sess['role'] = 'headteacher'
            sess['authenticated'] = True
            sess['csrf_token'] = 'test-csrf-token'
        return client
    
    @pytest.fixture
    def selenium_driver(self):
        """Create Selenium WebDriver for UI testing."""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in headless mode
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            yield driver
            driver.quit()
        except Exception as e:
            pytest.skip(f"Selenium WebDriver not available: {e}")
    
    def setup_comprehensive_test_data(self):
        """Set up comprehensive test data for integration testing."""
        # Create grades 1-9
        grades = []
        for i in range(1, 10):
            grade = Grade(name=f'Grade {i}')
            db.session.add(grade)
            grades.append(grade)
        
        # Create multiple streams per grade
        streams = []
        for grade in grades:
            for stream_name in ['A', 'B', 'C']:
                stream = Stream(name=stream_name, grade=grade)
                db.session.add(stream)
                streams.append(stream)
        
        # Create teachers
        headteacher = Teacher(
            name='Head Teacher',
            email='<EMAIL>',
            role='headteacher'
        )
        db.session.add(headteacher)
        
        classteacher = Teacher(
            name='Class Teacher',
            email='<EMAIL>',
            role='classteacher'
        )
        db.session.add(classteacher)
        
        # Create students across all grades and streams
        students = []
        student_id = 1
        for grade in grades:
            grade_streams = [s for s in streams if s.grade_id == grade.id]
            for stream in grade_streams:
                # Create 5 students per stream
                for i in range(5):
                    student = Student(
                        name=f'Student {student_id}',
                        admission_number=f'ADM{student_id:04d}',
                        grade=grade,
                        stream=stream,
                        academic_year='2024',
                        promotion_status='active',
                        is_eligible_for_promotion=True
                    )
                    db.session.add(student)
                    students.append(student)
                    student_id += 1
        
        db.session.commit()
        return grades, streams, students, headteacher, classteacher
    
    def test_end_to_end_promotion_workflow(self, headteacher_session):
        """Test complete end-to-end promotion workflow."""
        # Step 1: Access promotion management page
        response = headteacher_session.get('/headteacher/student-promotion')
        assert response.status_code == 200
        assert b'Student Promotion Management' in response.data
        
        # Step 2: Get promotion preview data
        response = headteacher_session.get('/headteacher/student-promotion/preview')
        assert response.status_code == 200
        preview_data = json.loads(response.data)
        assert 'statistics' in preview_data
        assert 'grades' in preview_data
        
        # Step 3: Process bulk promotion
        students_to_promote = []
        for grade_data in preview_data['grades'][:3]:  # First 3 grades
            for student in grade_data['students'][:2]:  # 2 students per grade
                students_to_promote.append({
                    'student_id': student['id'],
                    'action': 'promote',
                    'to_grade_id': student['grade_id'] + 1,
                    'to_stream_id': student['stream_id'],
                    'notes': f'End-to-end test promotion'
                })
        
        promotion_data = {
            'academic_year_to': '2025',
            'students': students_to_promote
        }
        
        with patch('new_structure.security.csrf_protection.CSRFProtection.validate_request', return_value=True):
            response = headteacher_session.post(
                '/headteacher/student-promotion/process',
                json=promotion_data,
                headers={'X-CSRFToken': 'valid-token'}
            )
            assert response.status_code == 200
            result = json.loads(response.data)
            assert result['success'] is True
            assert result['processed_count'] == len(students_to_promote)
        
        # Step 4: Verify promotion history
        response = headteacher_session.get('/headteacher/student-promotion/history')
        assert response.status_code == 200
        assert b'Promotion History' in response.data
        
        # Step 5: Check promotion statistics
        response = headteacher_session.get('/headteacher/student-promotion/statistics?academic_year=2025')
        assert response.status_code == 200
        stats = json.loads(response.data)
        assert stats['total_promotions'] >= len(students_to_promote)
    
    def test_integration_with_existing_student_management(self, app):
        """Test integration with existing student management features."""
        with app.app_context():
            # Get a student
            student = Student.query.first()
            original_grade = student.grade.name
            
            # Promote the student
            promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': student.id,
                        'action': 'promote',
                        'to_grade_id': student.grade_id + 1,
                        'to_stream_id': student.stream_id,
                        'notes': 'Integration test promotion'
                    }
                ]
            }
            
            result = StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            assert result['success'] is True
            
            # Verify student data integrity
            updated_student = Student.query.get(student.id)
            assert updated_student.name == student.name  # Name unchanged
            assert updated_student.admission_number == student.admission_number  # Admission number unchanged
            assert updated_student.grade_id == student.grade_id + 1  # Grade updated
            assert updated_student.academic_year == '2025'  # Academic year updated
            assert updated_student.promotion_status == 'promoted'  # Status updated
            
            # Verify relationships are maintained
            assert updated_student.grade is not None
            assert updated_student.stream is not None
            assert updated_student.stream.grade_id == updated_student.grade_id
    
    def test_database_consistency_after_bulk_operations(self, app):
        """Test database consistency after large bulk operations."""
        with app.app_context():
            # Get all eligible students
            students = Student.query.filter_by(promotion_status='active').limit(50).all()
            
            promotion_data = {
                'academic_year_to': '2025',
                'students': []
            }
            
            # Create mixed promotion actions
            for i, student in enumerate(students):
                action = ['promote', 'repeat', 'transfer'][i % 3]
                student_data = {
                    'student_id': student.id,
                    'action': action,
                    'notes': f'Bulk test {action}'
                }
                
                if action == 'promote' and student.grade_id < 9:
                    student_data['to_grade_id'] = student.grade_id + 1
                    student_data['to_stream_id'] = student.stream_id
                
                promotion_data['students'].append(student_data)
            
            # Process bulk promotion
            result = StudentPromotionService.process_bulk_promotion(
                promotion_data, 
                teacher_id=1
            )
            
            assert result['success'] is True
            
            # Verify database consistency
            # 1. All students should have updated academic year
            for student in students:
                updated_student = Student.query.get(student.id)
                assert updated_student.academic_year == '2025'
            
            # 2. History records should exist for all students
            history_count = StudentPromotionHistory.query.filter_by(academic_year_to='2025').count()
            assert history_count == len(students)
            
            # 3. Foreign key relationships should be intact
            for student in students:
                updated_student = Student.query.get(student.id)
                if updated_student.grade_id:
                    assert updated_student.grade is not None
                if updated_student.stream_id:
                    assert updated_student.stream is not None
    
    def test_error_handling_and_recovery(self, app):
        """Test error handling and recovery mechanisms."""
        with app.app_context():
            # Test with invalid student ID
            invalid_promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': 99999,  # Non-existent student
                        'action': 'promote',
                        'to_grade_id': 2,
                        'to_stream_id': 1
                    }
                ]
            }
            
            result = StudentPromotionService.process_bulk_promotion(
                invalid_promotion_data, 
                teacher_id=1
            )
            
            # Should handle error gracefully
            assert result['success'] is False
            assert 'error' in result
            
            # Test with mixed valid and invalid data
            valid_student = Student.query.first()
            mixed_promotion_data = {
                'academic_year_to': '2025',
                'students': [
                    {
                        'student_id': valid_student.id,
                        'action': 'promote',
                        'to_grade_id': valid_student.grade_id + 1,
                        'to_stream_id': valid_student.stream_id
                    },
                    {
                        'student_id': 99999,  # Invalid student
                        'action': 'promote',
                        'to_grade_id': 2,
                        'to_stream_id': 1
                    }
                ]
            }
            
            result = StudentPromotionService.process_bulk_promotion(
                mixed_promotion_data, 
                teacher_id=1
            )
            
            # Should process valid students and report errors for invalid ones
            assert 'processed_count' in result
            assert 'errors' in result or 'error' in result
    
    def test_mobile_responsive_design_simulation(self, headteacher_session):
        """Test mobile responsive design (simulated)."""
        # Simulate mobile user agent
        mobile_headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
        }
        
        # Test main promotion page
        response = headteacher_session.get(
            '/headteacher/student-promotion',
            headers=mobile_headers
        )
        assert response.status_code == 200
        
        # Check for mobile-responsive elements in HTML
        html_content = response.data.decode('utf-8')
        assert 'viewport' in html_content  # Viewport meta tag
        assert 'responsive' in html_content or 'mobile' in html_content.lower()
    
    def test_concurrent_promotion_operations(self, app):
        """Test handling of concurrent promotion operations."""
        with app.app_context():
            import threading
            import time
            
            students = Student.query.limit(10).all()
            results = []
            
            def promote_student(student_id, thread_id):
                try:
                    promotion_data = {
                        'academic_year_to': '2025',
                        'students': [
                            {
                                'student_id': student_id,
                                'action': 'promote',
                                'to_grade_id': Student.query.get(student_id).grade_id + 1,
                                'to_stream_id': Student.query.get(student_id).stream_id,
                                'notes': f'Concurrent test {thread_id}'
                            }
                        ]
                    }
                    
                    result = StudentPromotionService.process_bulk_promotion(
                        promotion_data, 
                        teacher_id=thread_id
                    )
                    results.append(result)
                except Exception as e:
                    results.append({'success': False, 'error': str(e)})
            
            # Create multiple threads for concurrent operations
            threads = []
            for i, student in enumerate(students[:5]):  # Test with 5 concurrent operations
                thread = threading.Thread(
                    target=promote_student, 
                    args=(student.id, i + 1)
                )
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            # Verify results
            assert len(results) == 5
            successful_operations = sum(1 for r in results if r.get('success', False))
            assert successful_operations >= 1  # At least one should succeed


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
