#!/usr/bin/env python3
"""
Test the subject configuration fixes.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_subject_config_api():
    """Test the subject configuration API endpoints."""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Subject Configuration API...")
    
    # Test 1: Check if save-all endpoint responds
    print("\n1️⃣ Testing Save All Configurations endpoint...")
    try:
        response = requests.post(f"{base_url}/api/subject-config/save-all", 
                               headers={'Content-Type': 'application/json'})
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {data}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Check composite detection for English
    print("\n2️⃣ Testing English composite detection...")
    try:
        response = requests.get(f"{base_url}/api/check-composite/english/upper_primary")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Is composite: {data.get('is_composite', False)}")
            print(f"   Components: {data.get('components', [])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Check composite detection for Kiswahili
    print("\n3️⃣ Testing Kiswahili composite detection...")
    try:
        response = requests.get(f"{base_url}/api/check-composite/kiswahili/junior_secondary")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Is composite: {data.get('is_composite', False)}")
            print(f"   Components: {data.get('components', [])}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Check all configurations
    print("\n4️⃣ Testing Get All Configurations...")
    try:
        response = requests.get(f"{base_url}/api/subject-config/all")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            configs = data.get('configurations', [])
            print(f"   Found {len(configs)} configurations")
            for config in configs:
                print(f"   - {config['subject_name']} ({config['education_level']}): {config['is_composite']}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_database_direct():
    """Test database directly."""
    print("\n🗄️ Testing Database Directly...")
    
    try:
        import pymysql
        from config import config
        
        conf = config['development']()
        
        connection = pymysql.connect(
            host=conf.MYSQL_HOST,
            user=conf.MYSQL_USER,
            password='@2494/lK',
            database=conf.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Check subject_configuration table
        cursor.execute("SELECT COUNT(*) FROM subject_configuration")
        config_count = cursor.fetchone()[0]
        print(f"   📊 subject_configuration records: {config_count}")
        
        # Check Subject table composite subjects
        cursor.execute("SELECT name, education_level, is_composite FROM subject WHERE is_composite = TRUE")
        composite_subjects = cursor.fetchall()
        print(f"   📚 Composite subjects in Subject table: {len(composite_subjects)}")
        for subject in composite_subjects:
            print(f"   - {subject[0]} ({subject[1]})")
        
        connection.close()
        print("   ✅ Database check completed")
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")

if __name__ == '__main__':
    print("🚀 Starting Subject Configuration Tests...")
    
    # Test database first
    test_database_direct()
    
    # Test API endpoints
    print("\n" + "="*50)
    test_subject_config_api()
    
    print("\n🎉 Tests completed!")
    print("\n📝 Next steps:")
    print("1. Start your Flask app: python run.py")
    print("2. Go to Subject Configuration in headteacher page")
    print("3. Click 'Save All Configuration' button")
    print("4. Check browser console for debug messages")
    print("5. Go to marks upload and select English/Kiswahili for upper_primary or junior_secondary")
