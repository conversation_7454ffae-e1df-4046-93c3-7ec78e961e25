<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Class Permission Management - Hillview School</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .stat-card i {
            font-size: 2rem;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .stat-card h3 {
            color: white;
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .stat-card p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .form-group {
            margin-bottom: 0;
        }

        .form-group label {
            display: block;
            color: white;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-control:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel h2 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .education-level {
            margin-bottom: 25px;
        }

        .education-level-header {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid #4CAF50;
        }

        .education-level-header:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .education-level-header h3 {
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0;
        }

        .class-list {
            display: none;
            padding-left: 20px;
        }

        .class-list.active {
            display: block;
        }

        .class-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #2196F3;
        }

        .class-item.multi-stream {
            border-left-color: #FF9800;
        }

        .class-name {
            color: white;
            font-weight: 500;
        }

        .class-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .type-single {
            background: rgba(33, 150, 243, 0.2);
            color: #2196F3;
        }

        .type-multi {
            background: rgba(255, 152, 0, 0.2);
            color: #FF9800;
        }

        .teacher-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .teacher-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }

        .teacher-info {
            flex: 1;
        }

        .teacher-name {
            color: white;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .teacher-username {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .teacher-role {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
        }

        .teacher-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination a, .pagination span {
            padding: 8px 12px;
            border-radius: 8px;
            text-decoration: none;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .pagination a:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .pagination .current {
            background: #4CAF50;
            border-color: #4CAF50;
        }

        .pagination .disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h1 style="margin: 0;">
                    <i class="fas fa-users-cog"></i>
                    Class Permission Management
                </h1>
                <a href="{{ url_for('universal.dashboard') }}" class="btn btn-outline" style="margin: 0;">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>
            <p>Manage teacher permissions for specific classes organized by education level</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <h3>{{ data.statistics.total_teachers }}</h3>
                <p>Total Teachers</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-key"></i>
                <h3>{{ data.statistics.total_permissions }}</h3>
                <p>Active Permissions</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-user-check"></i>
                <h3>{{ data.statistics.teachers_with_permissions }}</h3>
                <p>Teachers with Permissions</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-inbox"></i>
                <h3>{{ data.statistics.pending_requests }}</h3>
                <p>Pending Requests</p>
            </div>
        </div>

        <!-- Pending Requests Section -->
        <div class="panel" id="pending-requests-section" style="margin-bottom: 20px;">
            <h2>
                <i class="fas fa-inbox"></i>
                Pending Permission Requests
                <button onclick="loadPendingRequests()" class="btn btn-secondary btn-small" style="float: right;">
                    <i class="fas fa-refresh"></i>
                    Refresh
                </button>
            </h2>
            <div id="pending-requests-list">
                <div style="text-align: center; padding: 20px; color: rgba(255, 255, 255, 0.7);">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading pending requests...
                </div>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="controls-section">
            <form method="GET" id="filter-form">
                <div class="controls-grid">
                    <div class="form-group">
                        <label for="teacher-filter">Search Teachers:</label>
                        <input type="text" class="form-control" id="teacher-filter" name="teacher_filter" 
                               value="{{ data.filters.teacher_filter }}" 
                               placeholder="Search by name or username...">
                    </div>
                    
                    <div class="form-group">
                        <label for="role-filter">Filter by Role:</label>
                        <select class="form-control" id="role-filter" name="role_filter">
                            <option value="">All Roles</option>
                            <option value="classteacher" {% if data.filters.role_filter == 'classteacher' %}selected{% endif %}>Class Teacher</option>
                            <option value="teacher" {% if data.filters.role_filter == 'teacher' %}selected{% endif %}>Subject Teacher</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="per-page">Items per Page:</label>
                        <select class="form-control" id="per-page" name="per_page">
                            <option value="5" {% if data.pagination.per_page == 5 %}selected{% endif %}>5</option>
                            <option value="10" {% if data.pagination.per_page == 10 %}selected{% endif %}>10</option>
                            <option value="20" {% if data.pagination.per_page == 20 %}selected{% endif %}>20</option>
                            <option value="50" {% if data.pagination.per_page == 50 %}selected{% endif %}>50</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            Apply Filters
                        </button>
                        <a href="{{ url_for('permission.manage_permissions') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Classes Panel -->
            <div class="panel">
                <h2>
                    <i class="fas fa-school"></i>
                    Classes by Education Level
                </h2>

                {% for level_key, level_data in data.class_assignments.items() %}
                {% if level_data.classes %}
                <div class="education-level">
                    <div class="education-level-header" onclick="toggleEducationLevel('{{ level_key }}')">
                        <h3>
                            {{ level_data.name }} ({{ level_data.classes|length }} classes)
                            <i class="fas fa-chevron-down" id="icon-{{ level_key }}"></i>
                        </h3>
                    </div>
                    <div class="class-list" id="list-{{ level_key }}">
                        {% for class_info in level_data.classes %}
                        <div class="class-item {% if class_info.is_multi_stream %}multi-stream{% endif %}">
                            <div class="class-name">{{ class_info.class_name }}</div>
                            <div class="class-type {% if class_info.is_multi_stream %}type-multi{% else %}type-single{% endif %}">
                                {% if class_info.is_multi_stream %}Multi-Stream{% else %}Single Class{% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% endfor %}
            </div>

            <!-- Teachers Panel -->
            <div class="panel">
                <h2>
                    <i class="fas fa-users"></i>
                    Teachers ({{ data.pagination.total }} total)
                </h2>

                <div class="alert alert-success" id="success-alert"></div>
                <div class="alert alert-error" id="error-alert"></div>

                <div class="teacher-list">
                    {% for teacher in data.teachers %}
                    <div class="teacher-item">
                        <div class="teacher-info">
                            <div class="teacher-name">{{ teacher.name }}</div>
                            <div class="teacher-username">@{{ teacher.username }}</div>
                            <div class="teacher-role">{{ teacher.role.title() }}</div>
                        </div>
                        <div class="teacher-actions">
                            <button class="btn btn-primary btn-small" onclick="showGrantModal({{ teacher.id }}, '{{ teacher.name }}')">
                                <i class="fas fa-plus"></i>
                                Grant
                            </button>
                            <button class="btn btn-secondary btn-small" onclick="viewPermissions({{ teacher.id }})">
                                <i class="fas fa-eye"></i>
                                View
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if data.pagination.pages > 1 %}
                <div class="pagination">
                    {% if data.pagination.has_prev %}
                        <a href="{{ url_for('permission.manage_permissions', page=data.pagination.prev_num, per_page=data.pagination.per_page, teacher_filter=data.filters.teacher_filter, role_filter=data.filters.role_filter) }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% else %}
                        <span class="disabled"><i class="fas fa-chevron-left"></i></span>
                    {% endif %}

                    {% for page_num in range(1, data.pagination.pages + 1) %}
                        {% if page_num == data.pagination.page %}
                            <span class="current">{{ page_num }}</span>
                        {% else %}
                            <a href="{{ url_for('permission.manage_permissions', page=page_num, per_page=data.pagination.per_page, teacher_filter=data.filters.teacher_filter, role_filter=data.filters.role_filter) }}">{{ page_num }}</a>
                        {% endif %}
                    {% endfor %}

                    {% if data.pagination.has_next %}
                        <a href="{{ url_for('permission.manage_permissions', page=data.pagination.next_num, per_page=data.pagination.per_page, teacher_filter=data.filters.teacher_filter, role_filter=data.filters.role_filter) }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% else %}
                        <span class="disabled"><i class="fas fa-chevron-right"></i></span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        // Toggle education level visibility
        function toggleEducationLevel(level) {
            const list = document.getElementById(`list-${level}`);
            const icon = document.getElementById(`icon-${level}`);
            
            if (list.classList.contains('active')) {
                list.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            } else {
                list.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // Show grant permission modal
        function showGrantModal(teacherId, teacherName) {
            const modalHTML = `
                <div id="grantPermissionModal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                ">
                    <div style="
                        background: white;
                        border-radius: 15px;
                        padding: 30px;
                        max-width: 600px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0; color: #4CAF50;">
                                <i class="fas fa-user-plus"></i>
                                Grant Permission to ${teacherName}
                            </h3>
                            <button onclick="closeGrantModal()" style="
                                background: none;
                                border: none;
                                font-size: 1.5rem;
                                color: #999;
                                cursor: pointer;
                                padding: 5px;
                            ">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form id="grantPermissionForm">
                            <input type="hidden" id="grantTeacherId" value="${teacherId}">

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                                    Education Level *
                                </label>
                                <select id="grantEducationLevel" required style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 1px solid #ddd;
                                    border-radius: 8px;
                                    font-size: 1rem;
                                ">
                                    <option value="">Select education level...</option>
                                    <option value="lower_primary">Lower Primary (PP1, PP2, Grade 1-3)</option>
                                    <option value="upper_primary">Upper Primary (Grade 4-6)</option>
                                    <option value="junior_secondary">Junior Secondary (Grade 7-9)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                                    Grade/Class *
                                </label>
                                <select id="grantGrade" required style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 1px solid #ddd;
                                    border-radius: 8px;
                                    font-size: 1rem;
                                ">
                                    <option value="">Select a grade...</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                                    Stream (if applicable)
                                </label>
                                <select id="grantStream" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 1px solid #ddd;
                                    border-radius: 8px;
                                    font-size: 1rem;
                                ">
                                    <option value="">No specific stream</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                                    Notes (optional)
                                </label>
                                <textarea id="grantNotes" placeholder="Add any notes about this permission..." style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 1px solid #ddd;
                                    border-radius: 8px;
                                    min-height: 80px;
                                    resize: vertical;
                                "></textarea>
                            </div>

                            <div style="display: flex; gap: 15px; justify-content: flex-end;">
                                <button type="button" onclick="closeGrantModal()" style="
                                    padding: 12px 20px;
                                    border: 1px solid #ddd;
                                    background: white;
                                    color: #666;
                                    border-radius: 8px;
                                    cursor: pointer;
                                ">
                                    Cancel
                                </button>
                                <button type="submit" style="
                                    padding: 12px 20px;
                                    border: none;
                                    background: #4CAF50;
                                    color: white;
                                    border-radius: 8px;
                                    cursor: pointer;
                                ">
                                    <i class="fas fa-check"></i>
                                    Grant Permission
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Load class structure for the modal
            loadClassStructureForGrant();

            // Handle form submission
            document.getElementById('grantPermissionForm').addEventListener('submit', handleGrantPermission);
        }

        function closeGrantModal() {
            const modal = document.getElementById('grantPermissionModal');
            if (modal) {
                modal.remove();
            }
        }

        function loadClassStructureForGrant() {
            fetch('/permission/class_structure')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const educationLevelSelect = document.getElementById('grantEducationLevel');
                        const gradeSelect = document.getElementById('grantGrade');
                        const streamSelect = document.getElementById('grantStream');

                        // Store the full structure for filtering
                        window.grantClassStructure = data.structure;

                        // Education level mapping
                        const educationLevels = {
                            'lower_primary': ['PP1', 'PP2', 'Grade 1', 'Grade 2', 'Grade 3'],
                            'upper_primary': ['Grade 4', 'Grade 5', 'Grade 6'],
                            'junior_secondary': ['Grade 7', 'Grade 8', 'Grade 9']
                        };

                        // Handle education level change to filter grades
                        educationLevelSelect.addEventListener('change', function() {
                            gradeSelect.innerHTML = '<option value="">Select a grade...</option>';
                            streamSelect.innerHTML = '<option value="">No specific stream</option>';

                            if (this.value && educationLevels[this.value]) {
                                educationLevels[this.value].forEach(gradeName => {
                                    if (data.structure[gradeName]) {
                                        const option = document.createElement('option');
                                        option.value = gradeName;
                                        option.textContent = gradeName;
                                        gradeSelect.appendChild(option);
                                    }
                                });
                            }
                        });

                        // Handle grade change to update streams
                        gradeSelect.addEventListener('change', function() {
                            streamSelect.innerHTML = '<option value="">No specific stream</option>';

                            if (this.value && data.structure[this.value]) {
                                data.structure[this.value].forEach(stream => {
                                    const option = document.createElement('option');
                                    option.value = stream.name;
                                    option.textContent = stream.name;
                                    streamSelect.appendChild(option);
                                });
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading class structure:', error);
                    alert('Failed to load class structure. Please try again.');
                });
        }

        function handleGrantPermission(event) {
            event.preventDefault();

            const teacherId = document.getElementById('grantTeacherId').value;
            const gradeName = document.getElementById('grantGrade').value;
            const streamName = document.getElementById('grantStream').value;
            const notes = document.getElementById('grantNotes').value;

            if (!gradeName) {
                alert('Please select a grade');
                return;
            }

            // Submit the grant permission request
            fetch('/permission/grant', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken(),
                },
                body: JSON.stringify({
                    teacher_id: parseInt(teacherId),
                    grade_name: gradeName,
                    stream_name: streamName || null,
                    notes: notes.trim()
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                    closeGrantModal();
                    // Refresh the page to show updated permissions
                    window.location.reload();
                } else {
                    alert('❌ ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error granting permission:', error);
                alert('❌ Failed to grant permission. Please try again.');
            });
        }

        // View teacher permissions
        function viewPermissions(teacherId) {
            fetch(`/permission/teacher/${teacherId}/permissions`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const permissions = data.permissions;
                        if (permissions.length === 0) {
                            alert('This teacher has no class permissions assigned.');
                        } else {
                            let permissionList = 'Current Permissions:\\n\\n';
                            permissions.forEach(perm => {
                                permissionList += `• ${perm.grade_name}`;
                                if (perm.stream_name) {
                                    permissionList += ` - ${perm.stream_name}`;
                                }
                                permissionList += '\\n';
                            });
                            alert(permissionList);
                        }
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error viewing permissions:', error);
                    alert('❌ Failed to load permissions. Please try again.');
                });
        }

        // Get CSRF token
        function getCSRFToken() {
            return document.querySelector('meta[name=csrf-token]').getAttribute('content');
        }

        // Load pending permission requests
        function loadPendingRequests() {
            const requestsList = document.getElementById('pending-requests-list');
            requestsList.innerHTML = '<div style="text-align: center; padding: 20px; color: rgba(255, 255, 255, 0.7);"><i class="fas fa-spinner fa-spin"></i> Loading pending requests...</div>';

            fetch('/permission/requests')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const requests = data.requests;
                        if (requests.length === 0) {
                            requestsList.innerHTML = '<div style="text-align: center; padding: 20px; color: rgba(255, 255, 255, 0.7);"><i class="fas fa-check-circle"></i> No pending requests</div>';
                        } else {
                            let requestsHTML = '';
                            requests.forEach(req => {
                                const isFunction = req.reason && req.reason.includes('FUNCTION PERMISSION REQUEST');
                                const requestType = isFunction ? 'Function Permission' : 'Class Permission';
                                const requestIcon = isFunction ? 'fas fa-cog' : 'fas fa-school';

                                requestsHTML += `
                                    <div class="teacher-item" style="border-left: 4px solid #ff9800;">
                                        <div class="teacher-info">
                                            <div class="teacher-name">
                                                <i class="${requestIcon}"></i>
                                                ${req.teacher_name} - ${requestType}
                                            </div>
                                            <div class="teacher-username">
                                                ${isFunction ? 'Function Request' : (req.grade_name + (req.stream_name ? ' - ' + req.stream_name : ''))}
                                            </div>
                                            <div class="teacher-role" style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                                ${req.reason.substring(0, 100)}${req.reason.length > 100 ? '...' : ''}
                                            </div>
                                            <div style="font-size: 0.8rem; color: rgba(255, 255, 255, 0.5);">
                                                Requested: ${new Date(req.requested_at).toLocaleDateString()}
                                            </div>
                                        </div>
                                        <div class="teacher-actions">
                                            <button class="btn btn-primary btn-small" onclick="approveRequest(${req.id}, '${req.teacher_name}')">
                                                <i class="fas fa-check"></i>
                                                Approve
                                            </button>
                                            <button class="btn btn-secondary btn-small" onclick="viewRequestDetails(${req.id})">
                                                <i class="fas fa-eye"></i>
                                                Details
                                            </button>
                                            <button class="btn" style="background: #f44336; color: white;" onclick="denyRequest(${req.id}, '${req.teacher_name}')">
                                                <i class="fas fa-times"></i>
                                                Deny
                                            </button>
                                        </div>
                                    </div>
                                `;
                            });
                            requestsList.innerHTML = requestsHTML;
                        }
                    } else {
                        requestsList.innerHTML = '<div style="text-align: center; padding: 20px; color: #f44336;"><i class="fas fa-exclamation-triangle"></i> Failed to load requests</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading pending requests:', error);
                    requestsList.innerHTML = '<div style="text-align: center; padding: 20px; color: #f44336;"><i class="fas fa-exclamation-triangle"></i> Error loading requests</div>';
                });
        }

        // Approve permission request
        function approveRequest(requestId, teacherName) {
            if (confirm(`Approve permission request from ${teacherName}?`)) {
                fetch('/permission/process_request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCSRFToken(),
                    },
                    body: JSON.stringify({
                        request_id: requestId,
                        action: 'approve',
                        admin_notes: 'Approved by headteacher'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                        loadPendingRequests(); // Refresh the list
                        window.location.reload(); // Refresh to update statistics
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error approving request:', error);
                    alert('❌ Failed to approve request. Please try again.');
                });
            }
        }

        // Deny permission request
        function denyRequest(requestId, teacherName) {
            const reason = prompt(`Deny permission request from ${teacherName}?\\n\\nPlease provide a reason for denial:`);
            if (reason !== null && reason.trim()) {
                fetch('/permission/process_request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCSRFToken(),
                    },
                    body: JSON.stringify({
                        request_id: requestId,
                        action: 'deny',
                        admin_notes: reason.trim()
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                        loadPendingRequests(); // Refresh the list
                        window.location.reload(); // Refresh to update statistics
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error denying request:', error);
                    alert('❌ Failed to deny request. Please try again.');
                });
            }
        }

        // View request details
        function viewRequestDetails(requestId) {
            fetch(`/permission/requests`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const request = data.requests.find(r => r.id === requestId);
                        if (request) {
                            const isFunction = request.reason && request.reason.includes('FUNCTION PERMISSION REQUEST');
                            const details = `
Request Details:

Teacher: ${request.teacher_name}
Type: ${isFunction ? 'Function Permission' : 'Class Permission'}
${isFunction ? 'Function Request' : 'Grade: ' + request.grade_name + (request.stream_name ? '\\nStream: ' + request.stream_name : '')}
Requested: ${new Date(request.requested_at).toLocaleString()}

Reason:
${request.reason}
                            `;
                            alert(details);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading request details:', error);
                    alert('❌ Failed to load request details.');
                });
        }

        // Load pending requests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadPendingRequests();
        });

        // Show alert
        function showAlert(message, type) {
            const alertElement = document.getElementById(type === 'success' ? 'success-alert' : 'error-alert');
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }

        // Initialize - expand first education level
        document.addEventListener('DOMContentLoaded', function() {
            const firstLevel = document.querySelector('.education-level-header');
            if (firstLevel) {
                firstLevel.click();
            }
        });
    </script>
</body>
</html>
