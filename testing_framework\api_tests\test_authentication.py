"""
Sample API tests for Hillview School Management System.
"""

import pytest

class TestAuthenticationAPI:
    """Test authentication endpoints."""
    
    def test_admin_login_success(self, client):
        """Test successful admin login."""
        response = client.post('/admin_login', data={
            'username': 'test_headteacher',
            'password': 'test123'
        })
        assert response.status_code == 302
        assert '/headteacher/' in response.location
    
    def test_admin_login_invalid_credentials(self, client):
        """Test admin login with invalid credentials."""
        response = client.post('/admin_login', data={
            'username': 'invalid',
            'password': 'wrong'
        })
        assert response.status_code == 200  # Returns to login page
    
    def test_classteacher_login_success(self, client):
        """Test successful classteacher login."""
        response = client.post('/classteacher_login', data={
            'username': 'test_classteacher',
            'password': 'test123'
        })
        assert response.status_code == 302
        assert '/classteacher/' in response.location

class TestDashboardAPI:
    """Test dashboard endpoints."""
    
    def test_headteacher_dashboard_access(self, authenticated_headteacher):
        """Test headteacher can access dashboard."""
        response = authenticated_headteacher.get('/headteacher/')
        assert response.status_code == 200
    
    def test_classteacher_dashboard_access(self, authenticated_classteacher):
        """Test classteacher can access dashboard."""
        response = authenticated_classteacher.get('/classteacher/')
        assert response.status_code == 200
    
    def test_unauthorized_access_blocked(self, client):
        """Test unauthorized access is blocked."""
        response = client.get('/headteacher/')
        assert response.status_code == 302  # Redirect to login
