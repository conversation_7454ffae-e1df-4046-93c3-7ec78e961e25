"""
Performance tests for Hillview School Management System.
Tests load handling, response times, and system performance under stress.
"""

import pytest
import requests
import time
import concurrent.futures
from threading import Thread

@pytest.mark.performance
class TestResponseTimes:
    """Test response time performance."""
    
    def test_home_page_response_time(self, base_url, benchmark):
        """Test home page response time."""
        def get_home_page():
            response = requests.get(base_url)
            return response
        
        result = benchmark(get_home_page)
        assert result.status_code == 200
        # Response should be under 2 seconds
        assert benchmark.stats['mean'] < 2.0
    
    def test_health_check_response_time(self, base_url, benchmark):
        """Test health check endpoint response time."""
        def get_health():
            response = requests.get(f"{base_url}/health")
            return response
        
        result = benchmark(get_health)
        assert result.status_code == 200
        # Health check should be very fast (under 0.5 seconds)
        assert benchmark.stats['mean'] < 0.5
    
    def test_login_page_response_time(self, base_url, benchmark):
        """Test login page response time."""
        def get_login_page():
            response = requests.get(f"{base_url}/admin_login")
            return response
        
        result = benchmark(get_login_page)
        assert result.status_code == 200
        # Login page should load quickly
        assert benchmark.stats['mean'] < 1.5

@pytest.mark.performance
class TestConcurrentUsers:
    """Test system performance with concurrent users."""
    
    def test_concurrent_home_page_access(self, base_url):
        """Test concurrent access to home page."""
        def access_home_page():
            response = requests.get(base_url)
            return response.status_code == 200
        
        # Test with 10 concurrent users
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(access_home_page) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        assert all(results)
        assert len(results) == 10
    
    def test_concurrent_health_checks(self, base_url):
        """Test concurrent health check requests."""
        def health_check():
            response = requests.get(f"{base_url}/health")
            return response.status_code == 200
        
        # Test with 20 concurrent health checks
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(health_check) for _ in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All health checks should succeed
        assert all(results)
        assert len(results) == 20
    
    def test_concurrent_login_attempts(self, base_url):
        """Test concurrent login attempts."""
        def login_attempt():
            session = requests.Session()
            # Get login page first
            login_page = session.get(f"{base_url}/admin_login")
            if login_page.status_code != 200:
                return False
            
            # Attempt login
            login_data = {
                'username': 'headteacher',
                'password': 'admin123'
            }
            response = session.post(f"{base_url}/admin_login", data=login_data)
            return response.status_code in [200, 302]  # Success or redirect
        
        # Test with 5 concurrent login attempts
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(login_attempt) for _ in range(5)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Most login attempts should succeed
        success_rate = sum(results) / len(results)
        assert success_rate >= 0.8  # At least 80% success rate

@pytest.mark.performance
class TestMemoryUsage:
    """Test memory usage patterns."""
    
    def test_memory_stability_under_load(self, base_url):
        """Test memory stability under sustained load."""
        def make_requests():
            session = requests.Session()
            for _ in range(50):
                response = session.get(base_url)
                assert response.status_code == 200
                time.sleep(0.1)  # Small delay between requests
        
        # Run multiple threads making requests
        threads = []
        for _ in range(3):
            thread = Thread(target=make_requests)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # If we get here without errors, memory is stable
        assert True

@pytest.mark.performance
class TestDatabasePerformance:
    """Test database performance under load."""
    
    def test_database_query_performance(self, app, benchmark):
        """Test database query performance."""
        with app.app_context():
            from new_structure.models.user import Teacher
            
            def query_teachers():
                return Teacher.query.all()
            
            result = benchmark(query_teachers)
            assert len(result) >= 0
            # Database query should be fast
            assert benchmark.stats['mean'] < 0.1
    
    def test_concurrent_database_access(self, app):
        """Test concurrent database access."""
        def query_database():
            with app.app_context():
                from new_structure.models.user import Teacher
                teachers = Teacher.query.all()
                return len(teachers) >= 0
        
        # Test with 10 concurrent database queries
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(query_database) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All database queries should succeed
        assert all(results)

@pytest.mark.performance
class TestScalabilityLimits:
    """Test system scalability limits."""
    
    def test_maximum_concurrent_users(self, base_url):
        """Test system with maximum concurrent users."""
        def user_session():
            session = requests.Session()
            try:
                # Simulate user session
                response = session.get(base_url)
                if response.status_code == 200:
                    # Try to access a few pages
                    session.get(f"{base_url}/health")
                    session.get(f"{base_url}/admin_login")
                    return True
            except Exception:
                return False
            return False
        
        # Test with 50 concurrent users (adjust based on system capacity)
        max_users = 50
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_users) as executor:
            futures = [executor.submit(user_session) for _ in range(max_users)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # At least 70% of users should be served successfully
        success_rate = sum(results) / len(results)
        assert success_rate >= 0.7
    
    def test_sustained_load_performance(self, base_url):
        """Test performance under sustained load."""
        def sustained_requests():
            session = requests.Session()
            successful_requests = 0
            total_requests = 100
            
            start_time = time.time()
            for _ in range(total_requests):
                try:
                    response = session.get(base_url)
                    if response.status_code == 200:
                        successful_requests += 1
                    time.sleep(0.05)  # 20 requests per second
                except Exception:
                    pass
            
            end_time = time.time()
            duration = end_time - start_time
            
            return {
                'successful_requests': successful_requests,
                'total_requests': total_requests,
                'duration': duration,
                'requests_per_second': successful_requests / duration
            }
        
        # Run sustained load test
        result = sustained_requests()
        
        # Verify performance metrics
        assert result['successful_requests'] >= result['total_requests'] * 0.9  # 90% success rate
        assert result['requests_per_second'] >= 10  # At least 10 RPS

@pytest.mark.performance
class TestResourceUtilization:
    """Test resource utilization patterns."""
    
    def test_static_file_serving_performance(self, base_url, benchmark):
        """Test static file serving performance."""
        def get_static_file():
            # Try to get a CSS file
            response = requests.get(f"{base_url}/static/css/modern_classteacher.css")
            return response
        
        result = benchmark(get_static_file)
        # Static files should be served quickly
        if result.status_code == 200:
            assert benchmark.stats['mean'] < 0.5
    
    def test_error_handling_performance(self, base_url, benchmark):
        """Test error handling performance."""
        def trigger_404():
            response = requests.get(f"{base_url}/nonexistent-page")
            return response
        
        result = benchmark(trigger_404)
        assert result.status_code == 404
        # Error pages should also be fast
        assert benchmark.stats['mean'] < 1.0
