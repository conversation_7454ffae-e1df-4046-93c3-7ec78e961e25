<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grade Reports Dashboard - Hillview School</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        .header-section {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .header-title {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .header-subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
        }
        .selection-form {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .features-section {
            margin-top: 40px;
        }
        .feature-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 20px;
        }
        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: #2c3e50;
        }
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }
        .alert-info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mt-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('classteacher.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">Grade Reports</li>
            </ol>
        </nav>

        <div class="dashboard-container">
            <!-- Header -->
            <div class="header-section">
                <h1 class="header-title">
                    <i class="fas fa-chart-line me-3"></i>
                    Grade Reports Dashboard
                </h1>
                <p class="header-subtitle">
                    Generate comprehensive reports for entire grades with multiple streams
                </p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Selection Form -->
            <div class="selection-form">
                <h3 class="mb-4">
                    <i class="fas fa-filter me-2"></i>
                    Select Grade and Assessment
                </h3>
                
                <form id="gradeReportForm">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="grade" class="form-label">Grade</label>
                                <select class="form-select" id="grade" name="grade" required>
                                    <option value="">Select Grade</option>
                                    {% for grade in grades %}
                                        <option value="{{ grade.name }}">{{ grade.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="term" class="form-label">Term</label>
                                <select class="form-select" id="term" name="term" required>
                                    <option value="">Select Term</option>
                                    {% for term in terms %}
                                        <option value="{{ term.name }}">{{ term.name.replace('_', ' ').title() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="assessment_type" class="form-label">Assessment Type</label>
                                <select class="form-select" id="assessment_type" name="assessment_type" required>
                                    <option value="">Select Assessment</option>
                                    {% for assessment in assessment_types %}
                                        <option value="{{ assessment.name }}">{{ assessment.name.replace('_', ' ').title() }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i>
                            View Grade Streams
                        </button>
                    </div>
                </form>
            </div>

            <!-- Features Section -->
            <div class="features-section">
                <h3 class="mb-4 text-center">
                    <i class="fas fa-star me-2"></i>
                    Enhanced Features
                </h3>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <h4 class="feature-title">Multi-Stream Support</h4>
                            <p class="feature-description">
                                Automatically detects and handles grades with multiple streams (A, B, C, D, etc.)
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <h4 class="feature-title">Individual Stream Reports</h4>
                            <p class="feature-description">
                                Generate separate detailed reports for each stream within a grade
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h4 class="feature-title">Consolidated Grade Report</h4>
                            <p class="feature-description">
                                Combined analysis report showing performance across all streams in the grade
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <h4 class="feature-title">Batch Download</h4>
                            <p class="feature-description">
                                Download all stream reports and consolidated report in a single ZIP file
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h4 class="feature-title">Flexible Configuration</h4>
                            <p class="feature-description">
                                Works with any number of streams - from single class grades to 4+ stream grades
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card text-center">
                            <div class="feature-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <h4 class="feature-title">Advanced Analytics</h4>
                            <p class="feature-description">
                                Cross-stream comparisons, grade-level statistics, and performance insights
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Alert -->
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>How it works:</strong> Select a grade, term, and assessment type to view all streams in that grade. 
                You can then generate individual stream reports, a consolidated grade report, or download everything in a batch.
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('gradeReportForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const grade = document.getElementById('grade').value;
            const term = document.getElementById('term').value;
            const assessmentType = document.getElementById('assessment_type').value;
            
            if (grade && term && assessmentType) {
                window.location.href = `/classteacher/grade_streams_status/${grade}/${term}/${assessmentType}`;
            } else {
                alert('Please select all required fields.');
            }
        });
    </script>
</body>
</html>
