"""
Comprehensive E2E tests using <PERSON><PERSON> for Hillview School Management System.
Tests complete user journeys across all roles and features.
"""

import pytest
import time

@pytest.mark.e2e
class TestAuthenticationJourneys:
    """Test authentication user journeys."""

    def test_headteacher_login_journey(self, page, base_url):
        """Test complete headteacher login journey."""
        # Navigate to home page
        page.goto(base_url)
        page.wait_for_load_state('networkidle')

        # Click on admin login
        page.click('text=Admin Login')
        page.wait_for_url(f"{base_url}/admin_login")

        # Fill login form
        page.fill('input[name="username"]', 'headteacher')
        page.fill('input[name="password"]', 'admin123')

        # Submit form
        page.click('button[type="submit"]')

        # Wait for redirect and verify dashboard
        page.wait_for_url(f"{base_url}/headteacher/", timeout=10000)

        # Verify dashboard elements
        assert page.is_visible('text=Dashboard') or page.is_visible('text=Welcome')

    def test_classteacher_login_journey(self, page, base_url):
        """Test complete classteacher login journey."""
        # Navigate to home page
        page.goto(base_url)
        page.wait_for_load_state('networkidle')

        # Click on classteacher login
        page.click('text=Classteacher Login')
        page.wait_for_url(f"{base_url}/classteacher_login")

        # Fill login form
        page.fill('input[name="username"]', 'kevin')
        page.fill('input[name="password"]', 'kev123')

        # Submit form
        page.click('button[type="submit"]')

        # Wait for redirect and verify dashboard
        page.wait_for_url(f"{base_url}/classteacher/", timeout=10000)

        # Verify dashboard elements
        assert page.is_visible('text=Dashboard') or page.is_visible('text=Welcome')

    def test_teacher_login_journey(self, page, base_url):
        """Test complete teacher login journey."""
        # Navigate to home page
        page.goto(base_url)
        page.wait_for_load_state('networkidle')

        # Click on teacher login
        page.click('text=Teacher Login')
        page.wait_for_url(f"{base_url}/teacher_login")

        # Fill login form
        page.fill('input[name="username"]', 'telvo')
        page.fill('input[name="password"]', 'telvo123')

        # Submit form
        page.click('button[type="submit"]')

        # Wait for redirect and verify dashboard
        page.wait_for_url(f"{base_url}/teacher/", timeout=10000)

        # Verify dashboard elements
        assert page.is_visible('text=Dashboard') or page.is_visible('text=Welcome')

@pytest.mark.e2e
class TestHeadteacherJourneys:
    """Test headteacher specific journeys."""

    def test_headteacher_dashboard_navigation(self, page, base_url):
        """Test headteacher dashboard navigation."""
        # Login as headteacher
        self._login_as_headteacher(page, base_url)

        # Test navigation elements
        navigation_items = [
            'Dashboard',
            'Teachers',
            'Students',
            'Analytics',
            'Reports'
        ]

        for item in navigation_items:
            if page.is_visible(f'text={item}'):
                page.click(f'text={item}')
                page.wait_for_load_state('networkidle')
                # Verify page loaded
                assert page.url != f"{base_url}/admin_login"

    def test_headteacher_teacher_management(self, page, base_url):
        """Test headteacher teacher management features."""
        # Login as headteacher
        self._login_as_headteacher(page, base_url)

        # Navigate to teacher management
        if page.is_visible('text=Manage Teachers'):
            page.click('text=Manage Teachers')
            page.wait_for_load_state('networkidle')

            # Verify teacher management page
            assert 'teacher' in page.url.lower() or 'manage' in page.url.lower()

    def _login_as_headteacher(self, page, base_url):
        """Helper method to login as headteacher."""
        page.goto(f"{base_url}/admin_login")
        page.fill('input[name="username"]', 'headteacher')
        page.fill('input[name="password"]', 'admin123')
        page.click('button[type="submit"]')
        page.wait_for_url(f"{base_url}/headteacher/", timeout=10000)

@pytest.mark.e2e
class TestClassteacherJourneys:
    """Test classteacher specific journeys."""

    def test_classteacher_dashboard_features(self, page, base_url):
        """Test classteacher dashboard features."""
        # Login as classteacher
        self._login_as_classteacher(page, base_url)

        # Test available features
        features = [
            'Upload Marks',
            'Generate Reports',
            'View Students',
            'Analytics'
        ]

        for feature in features:
            if page.is_visible(f'text={feature}'):
                page.click(f'text={feature}')
                page.wait_for_load_state('networkidle')
                # Verify feature is accessible
                assert page.url != f"{base_url}/classteacher_login"

    def test_classteacher_marks_upload_flow(self, page, base_url):
        """Test classteacher marks upload flow."""
        # Login as classteacher
        self._login_as_classteacher(page, base_url)

        # Navigate to marks upload
        if page.is_visible('text=Upload Marks'):
            page.click('text=Upload Marks')
            page.wait_for_load_state('networkidle')

            # Verify marks upload page elements
            if page.is_visible('select[name="grade"]'):
                assert page.is_visible('select[name="grade"]')
            if page.is_visible('select[name="subject"]'):
                assert page.is_visible('select[name="subject"]')

    def _login_as_classteacher(self, page, base_url):
        """Helper method to login as classteacher."""
        page.goto(f"{base_url}/classteacher_login")
        page.fill('input[name="username"]', 'kevin')
        page.fill('input[name="password"]', 'kev123')
        page.click('button[type="submit"]')
        page.wait_for_url(f"{base_url}/classteacher/", timeout=10000)

@pytest.mark.e2e
class TestSecurityJourneys:
    """Test security-related user journeys."""

    def test_unauthorized_access_blocked(self, page, base_url):
        """Test that unauthorized access is properly blocked."""
        # Try to access headteacher dashboard without login
        page.goto(f"{base_url}/headteacher/")

        # Should be redirected to login
        page.wait_for_load_state('networkidle')
        assert 'login' in page.url.lower() or page.url == base_url

    def test_invalid_login_handling(self, page, base_url):
        """Test handling of invalid login credentials."""
        # Navigate to login page
        page.goto(f"{base_url}/admin_login")

        # Try invalid credentials
        page.fill('input[name="username"]', 'invalid_user')
        page.fill('input[name="password"]', 'wrong_password')
        page.click('button[type="submit"]')

        # Should stay on login page
        page.wait_for_load_state('networkidle')
        assert 'login' in page.url.lower()

    def test_logout_functionality(self, page, base_url):
        """Test logout functionality."""
        # Login as headteacher
        page.goto(f"{base_url}/admin_login")
        page.fill('input[name="username"]', 'headteacher')
        page.fill('input[name="password"]', 'admin123')
        page.click('button[type="submit"]')
        page.wait_for_url(f"{base_url}/headteacher/", timeout=10000)

        # Logout
        if page.is_visible('text=Logout'):
            page.click('text=Logout')
            page.wait_for_load_state('networkidle')

            # Should be redirected to home or login
            assert page.url == base_url or 'login' in page.url.lower()

@pytest.mark.e2e
class TestResponsivenessJourneys:
    """Test responsive design across different screen sizes."""

    def test_mobile_responsiveness(self, page, base_url):
        """Test mobile responsiveness."""
        # Set mobile viewport
        page.set_viewport_size({"width": 375, "height": 667})

        # Navigate to home page
        page.goto(base_url)
        page.wait_for_load_state('networkidle')

        # Verify page is responsive
        assert page.is_visible('body')

        # Test login on mobile
        if page.is_visible('text=Admin Login'):
            page.click('text=Admin Login')
            page.wait_for_load_state('networkidle')

            # Verify login form is visible on mobile
            assert page.is_visible('input[name="username"]')
            assert page.is_visible('input[name="password"]')

    def test_tablet_responsiveness(self, page, base_url):
        """Test tablet responsiveness."""
        # Set tablet viewport
        page.set_viewport_size({"width": 768, "height": 1024})

        # Navigate to home page
        page.goto(base_url)
        page.wait_for_load_state('networkidle')

        # Verify page is responsive
        assert page.is_visible('body')

    def test_desktop_responsiveness(self, page, base_url):
        """Test desktop responsiveness."""
        # Set desktop viewport
        page.set_viewport_size({"width": 1920, "height": 1080})

        # Navigate to home page
        page.goto(base_url)
        page.wait_for_load_state('networkidle')

        # Verify page is responsive
        assert page.is_visible('body')
