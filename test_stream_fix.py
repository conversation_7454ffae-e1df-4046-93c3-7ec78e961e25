#!/usr/bin/env python3
"""
Test script to verify the stream loading fix
"""

import requests
import json
import time

def test_stream_endpoints():
    """Test both stream API endpoints with correct URLs"""
    print("🧪 TESTING STREAM API ENDPOINTS AFTER FIX")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    test_grade_id = 21  # Grade 9 with streams B, G, Y
    
    # Test endpoints
    endpoints = [
        {
            'name': 'Classteacher Stream API',
            'url': f"{base_url}/classteacher/get_streams/{test_grade_id}",
            'expected_format': 'classteacher'
        },
        {
            'name': 'Headteacher Universal Stream API',
            'url': f"{base_url}/universal/api/streams/{test_grade_id}",
            'expected_format': 'headteacher'
        }
    ]
    
    print("🔍 Testing API endpoints without authentication (should get 403)...")
    for endpoint in endpoints:
        print(f"\n📡 Testing: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        
        try:
            response = requests.get(endpoint['url'])
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 403:
                print("   ✅ Correctly requires authentication")
            elif response.status_code == 404:
                print("   ❌ Endpoint not found - URL might be incorrect")
            elif response.status_code == 200:
                print("   ⚠️ Unexpectedly accessible without auth")
                try:
                    data = response.json()
                    print(f"   Data: {json.dumps(data, indent=2)}")
                except:
                    print(f"   Response: {response.text[:100]}...")
            else:
                print(f"   ❓ Unexpected status: {response.status_code}")
                print(f"   Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
    
    return True

def test_javascript_fix():
    """Test the JavaScript fix by checking the template content"""
    print("\n🔧 VERIFYING JAVASCRIPT FIX IN TEMPLATE")
    print("=" * 50)
    
    template_path = "templates/manage_students.html"
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for old incorrect URLs
        old_pattern = "/headteacher/universal/api/streams"
        new_pattern = "/universal/api/streams"
        
        old_count = content.count(old_pattern)
        new_count = content.count(new_pattern)
        
        print(f"📊 Template Analysis:")
        print(f"   Old pattern '{old_pattern}': {old_count} occurrences")
        print(f"   New pattern '{new_pattern}': {new_count} occurrences")
        
        if old_count == 0:
            print("   ✅ All old incorrect URLs have been fixed!")
        else:
            print("   ❌ Still found old incorrect URLs")
            
        if new_count > 0:
            print("   ✅ New correct URLs are present")
        else:
            print("   ❌ No new correct URLs found")
            
        # Show context of the fixes
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if new_pattern in line and 'apiEndpoint' in line:
                print(f"\n📝 Fixed line {i+1}:")
                print(f"   {line.strip()}")
                
    except FileNotFoundError:
        print(f"❌ Template file not found: {template_path}")
    except Exception as e:
        print(f"❌ Error reading template: {e}")

def test_blueprint_registration():
    """Test that the universal blueprint is properly registered"""
    print("\n🔗 TESTING BLUEPRINT REGISTRATION")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    # Test a simple universal endpoint that should exist
    test_endpoints = [
        f"{base_url}/universal/dashboard",
        f"{base_url}/universal/api/streams/21"
    ]
    
    for endpoint in test_endpoints:
        try:
            response = requests.get(endpoint)
            print(f"📡 {endpoint}")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 404:
                print("   ❌ Blueprint not registered or route not found")
            elif response.status_code == 403:
                print("   ✅ Blueprint registered, requires authentication")
            elif response.status_code == 302:
                print("   ✅ Blueprint registered, redirecting (likely to login)")
            else:
                print(f"   ℹ️ Other status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Request failed: {e}")

def main():
    """Main function"""
    print("🚀 Stream Loading Fix Verification")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    test_stream_endpoints()
    test_javascript_fix()
    test_blueprint_registration()
    
    print("\n" + "=" * 60)
    print("📋 MANUAL TESTING STEPS:")
    print("=" * 60)
    print("1. Login as headteacher: http://localhost:5000/admin_login")
    print("2. Go to Universal Access → Manage Students")
    print("3. Select 'Junior Secondary' education level")
    print("4. Select 'Grade 9' from the grade dropdown")
    print("5. Check if streams B, G, Y appear in the stream dropdown")
    print("6. Open browser dev tools and check Network tab for API calls")
    print("7. Verify no 404 errors for stream API endpoints")
    
    print("\n🔧 EXPECTED RESULTS:")
    print("- Stream dropdown should populate with: B, G, Y")
    print("- No 'Error loading streams' message")
    print("- API call to /universal/api/streams/21 should return 200 (when authenticated)")
    print("- No JavaScript errors in browser console")

if __name__ == "__main__":
    main()
