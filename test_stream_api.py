#!/usr/bin/env python3
"""
Test script to check stream API endpoints
"""

import requests
import json

def test_stream_endpoints():
    """Test the stream API endpoints"""
    base_url = "http://localhost:5000"
    
    # Test data - we know Grade 9 (ID: 21) has streams
    grade_id = 21
    
    print("🧪 Testing Stream API Endpoints")
    print("=" * 50)
    
    # Test classteacher endpoint
    classteacher_url = f"{base_url}/classteacher/get_streams/{grade_id}"
    print(f"\n1. Testing Classteacher Endpoint:")
    print(f"   URL: {classteacher_url}")
    
    try:
        response = requests.get(classteacher_url)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    # Test headteacher universal endpoint
    headteacher_url = f"{base_url}/headteacher/universal/api/streams/{grade_id}"
    print(f"\n2. Testing Headteacher Universal Endpoint:")
    print(f"   URL: {headteacher_url}")
    
    try:
        response = requests.get(headteacher_url)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    # Test with invalid grade ID
    invalid_grade_id = 999
    invalid_url = f"{base_url}/classteacher/get_streams/{invalid_grade_id}"
    print(f"\n3. Testing Invalid Grade ID:")
    print(f"   URL: {invalid_url}")
    
    try:
        response = requests.get(invalid_url)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {str(e)}")
    
    print("\n" + "=" * 50)
    print("✅ Stream API Test Complete")

if __name__ == "__main__":
    test_stream_endpoints()
