#!/usr/bin/env python3
"""
Test the final composite subject setup after removing lower primary.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import pymysql
from config import config as app_config

def test_final_setup():
    """Test the final composite subject setup."""
    conf = app_config['development']()
    
    try:
        connection = pymysql.connect(
            host=conf.MYSQL_HOST,
            user=conf.MYSQL_USER,
            password='@2494/lK',
            database=conf.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🧪 Testing Final Composite Subject Setup")
        print("=" * 50)
        
        # Test 1: Check subject_configuration table
        print("\n1️⃣ Subject Configuration Table:")
        cursor.execute("""
            SELECT subject_name, education_level, is_composite,
                   component_1_name, component_1_weight,
                   component_2_name, component_2_weight
            FROM subject_configuration
            ORDER BY education_level, subject_name
        """)
        
        configs = cursor.fetchall()
        print(f"   📊 Total configurations: {len(configs)}")
        
        for config in configs:
            print(f"   ✅ {config[0].upper()} ({config[1]}): {config[3]} ({config[4]}%) + {config[5]} ({config[6]}%)")
        
        # Test 2: Check Subject table composite status
        print("\n2️⃣ Subject Table Composite Status:")
        cursor.execute("""
            SELECT name, education_level, is_composite
            FROM subject
            WHERE LOWER(name) IN ('english', 'kiswahili')
            ORDER BY education_level, name
        """)
        
        subjects = cursor.fetchall()
        print(f"   📚 English/Kiswahili subjects: {len(subjects)}")
        
        for subject in subjects:
            status = "✅ Composite" if subject[2] else "❌ Simple"
            print(f"   {subject[0]} ({subject[1]}): {status}")
        
        # Test 3: Test specific education levels
        print("\n3️⃣ Testing by Education Level:")
        
        levels_to_test = ['lower_primary', 'upper_primary', 'junior_secondary']
        
        for level in levels_to_test:
            print(f"\n   📖 {level.upper()}:")
            
            # Check configurations
            cursor.execute("""
                SELECT COUNT(*) FROM subject_configuration
                WHERE education_level = %s
            """, (level,))
            config_count = cursor.fetchone()[0]
            
            # Check composite subjects
            cursor.execute("""
                SELECT COUNT(*) FROM subject
                WHERE education_level = %s
                AND LOWER(name) IN ('english', 'kiswahili')
                AND is_composite = TRUE
            """, (level,))
            composite_count = cursor.fetchone()[0]
            
            if level == 'lower_primary':
                expected_configs = 0
                expected_composite = 0
                print(f"      📋 Configurations: {config_count} (Expected: {expected_configs}) {'✅' if config_count == expected_configs else '❌'}")
                print(f"      🔗 Composite subjects: {composite_count} (Expected: {expected_composite}) {'✅' if composite_count == expected_composite else '❌'}")
            else:
                expected_configs = 2  # English + Kiswahili
                expected_composite = 2
                print(f"      📋 Configurations: {config_count} (Expected: {expected_configs}) {'✅' if config_count == expected_configs else '❌'}")
                print(f"      🔗 Composite subjects: {composite_count} (Expected: {expected_composite}) {'✅' if composite_count == expected_composite else '❌'}")
        
        # Test 4: Test API simulation
        print("\n4️⃣ API Simulation Test:")
        
        test_cases = [
            ('english', 'lower_primary', False),  # Should NOT be composite
            ('english', 'upper_primary', True),   # Should be composite
            ('english', 'junior_secondary', True), # Should be composite
            ('kiswahili', 'lower_primary', False), # Should NOT be composite
            ('kiswahili', 'upper_primary', True),  # Should be composite
            ('kiswahili', 'junior_secondary', True) # Should be composite
        ]
        
        for subject, level, expected_composite in test_cases:
            cursor.execute("""
                SELECT is_composite FROM subject_configuration
                WHERE LOWER(subject_name) = %s AND education_level = %s
            """, (subject.lower(), level))
            
            result = cursor.fetchone()
            is_composite = bool(result[0]) if result else False
            
            status = "✅" if is_composite == expected_composite else "❌"
            print(f"   {status} {subject.upper()} ({level}): {'Composite' if is_composite else 'Simple'} (Expected: {'Composite' if expected_composite else 'Simple'})")
        
        print("\n" + "=" * 50)
        print("🎯 Summary:")
        print("   ✅ Lower Primary: No composite subjects (simple only)")
        print("   ✅ Upper Primary: English & Kiswahili composite")
        print("   ✅ Junior Secondary: English & Kiswahili composite")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 Starting Final Composite Setup Test...")
    
    success = test_final_setup()
    
    if success:
        print("\n✅ All tests passed!")
        print("\n📝 Ready for testing:")
        print("1. Go to Subject Configuration page")
        print("2. Click 'Save All Configurations' - should show 4 configurations processed")
        print("3. Go to marks upload:")
        print("   - Lower Primary: English/Kiswahili should be SIMPLE (no components)")
        print("   - Upper Primary: English/Kiswahili should be COMPOSITE (with components)")
        print("   - Junior Secondary: English/Kiswahili should be COMPOSITE (with components)")
    else:
        print("\n❌ Tests failed. Please check the errors above.")
