"""
Database integration tests for Hillview School Management System.
Tests CRUD operations, data integrity, and database performance.
"""

import pytest
from new_structure.extensions import db
from new_structure.models.user import Teacher
from new_structure.models.academic import Subject, Grade, Stream

@pytest.mark.database
class TestDatabaseCRUD:
    """Test database CRUD operations."""
    
    def test_create_teacher(self, app):
        """Test creating a new teacher."""
        with app.app_context():
            teacher = Teacher(
                username='test_new_teacher',
                password='password123',
                role='teacher'
            )
            
            db.session.add(teacher)
            db.session.commit()
            
            # Verify teacher was created
            created_teacher = Teacher.query.filter_by(username='test_new_teacher').first()
            assert created_teacher is not None
            assert created_teacher.username == 'test_new_teacher'
            assert created_teacher.role == 'teacher'
    
    def test_read_teachers(self, app):
        """Test reading teachers from database."""
        with app.app_context():
            teachers = Teacher.query.all()
            assert len(teachers) >= 3  # At least our test users
            
            # Check test users exist
            usernames = [teacher.username for teacher in teachers]
            assert 'test_headteacher' in usernames
            assert 'test_classteacher' in usernames
            assert 'test_teacher' in usernames
    
    def test_update_teacher(self, app):
        """Test updating teacher information."""
        with app.app_context():
            teacher = Teacher.query.filter_by(username='test_teacher').first()
            assert teacher is not None
            
            # Update teacher
            original_role = teacher.role
            teacher.role = 'classteacher'
            db.session.commit()
            
            # Verify update
            updated_teacher = Teacher.query.filter_by(username='test_teacher').first()
            assert updated_teacher.role == 'classteacher'
            
            # Restore original role
            teacher.role = original_role
            db.session.commit()
    
    def test_delete_teacher(self, app):
        """Test deleting a teacher."""
        with app.app_context():
            # Create a teacher to delete
            teacher = Teacher(
                username='test_delete_teacher',
                password='password123',
                role='teacher'
            )
            db.session.add(teacher)
            db.session.commit()
            
            teacher_id = teacher.id
            
            # Delete teacher
            db.session.delete(teacher)
            db.session.commit()
            
            # Verify deletion
            deleted_teacher = Teacher.query.get(teacher_id)
            assert deleted_teacher is None

@pytest.mark.database
class TestDataIntegrity:
    """Test data integrity and constraints."""
    
    def test_unique_username_constraint(self, app):
        """Test that usernames must be unique."""
        with app.app_context():
            # Try to create teacher with existing username
            duplicate_teacher = Teacher(
                username='test_headteacher',  # This already exists
                password='password123',
                role='teacher'
            )
            
            db.session.add(duplicate_teacher)
            
            # This should raise an integrity error
            with pytest.raises(Exception):
                db.session.commit()
            
            db.session.rollback()
    
    def test_required_fields(self, app):
        """Test that required fields are enforced."""
        with app.app_context():
            # Try to create teacher without username
            incomplete_teacher = Teacher(
                password='password123',
                role='teacher'
            )
            
            db.session.add(incomplete_teacher)
            
            # This should raise an error
            with pytest.raises(Exception):
                db.session.commit()
            
            db.session.rollback()

@pytest.mark.database
@pytest.mark.performance
class TestDatabasePerformance:
    """Test database performance."""
    
    def test_teacher_query_performance(self, app, benchmark):
        """Test teacher query performance."""
        with app.app_context():
            # Benchmark teacher query
            result = benchmark(Teacher.query.all)
            assert len(result) >= 3
    
    def test_bulk_teacher_creation(self, app):
        """Test creating multiple teachers efficiently."""
        with app.app_context():
            # Create 100 test teachers
            teachers = []
            for i in range(100):
                teacher = Teacher(
                    username=f'bulk_teacher_{i}',
                    password='password123',
                    role='teacher'
                )
                teachers.append(teacher)
            
            # Bulk insert
            db.session.add_all(teachers)
            db.session.commit()
            
            # Verify all were created
            bulk_teachers = Teacher.query.filter(
                Teacher.username.like('bulk_teacher_%')
            ).all()
            assert len(bulk_teachers) == 100
            
            # Cleanup
            for teacher in bulk_teachers:
                db.session.delete(teacher)
            db.session.commit()

@pytest.mark.database
class TestDatabaseTransactions:
    """Test database transaction handling."""
    
    def test_transaction_rollback(self, app):
        """Test transaction rollback on error."""
        with app.app_context():
            initial_count = Teacher.query.count()
            
            try:
                # Start transaction
                teacher1 = Teacher(
                    username='transaction_test_1',
                    password='password123',
                    role='teacher'
                )
                db.session.add(teacher1)
                
                # This should cause an error (duplicate username)
                teacher2 = Teacher(
                    username='transaction_test_1',  # Duplicate
                    password='password123',
                    role='teacher'
                )
                db.session.add(teacher2)
                db.session.commit()
                
            except Exception:
                db.session.rollback()
            
            # Verify no teachers were added
            final_count = Teacher.query.count()
            assert final_count == initial_count
    
    def test_transaction_commit(self, app):
        """Test successful transaction commit."""
        with app.app_context():
            initial_count = Teacher.query.count()
            
            # Create multiple teachers in one transaction
            teachers = [
                Teacher(username='trans_teacher_1', password='pass123', role='teacher'),
                Teacher(username='trans_teacher_2', password='pass123', role='classteacher'),
                Teacher(username='trans_teacher_3', password='pass123', role='headteacher')
            ]
            
            for teacher in teachers:
                db.session.add(teacher)
            
            db.session.commit()
            
            # Verify all were added
            final_count = Teacher.query.count()
            assert final_count == initial_count + 3
            
            # Cleanup
            for teacher in teachers:
                db.session.delete(teacher)
            db.session.commit()

@pytest.mark.database
class TestDatabaseConnections:
    """Test database connection handling."""
    
    def test_database_connection(self, app):
        """Test database connection is working."""
        with app.app_context():
            # Simple query to test connection
            result = db.session.execute(db.text('SELECT 1')).scalar()
            assert result == 1
    
    def test_multiple_connections(self, app):
        """Test handling multiple database operations."""
        with app.app_context():
            # Perform multiple operations
            operations = [
                lambda: Teacher.query.count(),
                lambda: Teacher.query.filter_by(role='headteacher').count(),
                lambda: Teacher.query.filter_by(role='classteacher').count(),
                lambda: Teacher.query.filter_by(role='teacher').count()
            ]
            
            results = []
            for operation in operations:
                results.append(operation())
            
            # Verify all operations completed
            assert all(isinstance(result, int) for result in results)
            assert sum(results[1:]) == results[0]  # Role counts should sum to total
