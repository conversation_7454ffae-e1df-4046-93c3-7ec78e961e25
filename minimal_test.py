#!/usr/bin/env python3
"""
Minimal test to identify the hanging issue.
"""

print("🔍 Starting minimal test...")

# Test 1: Basic Flask
print("Test 1: Basic Flask import and creation")
try:
    from flask import Flask
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return "Hello World!"
    
    print("✅ Basic Flask app created")
except Exception as e:
    print(f"❌ Basic Flask failed: {e}")
    exit(1)

# Test 2: Flask with config
print("Test 2: Flask with configuration")
try:
    app.config['SECRET_KEY'] = 'test'
    print("✅ Configuration added")
except Exception as e:
    print(f"❌ Configuration failed: {e}")

# Test 3: Import SQLAlchemy
print("Test 3: SQLAlchemy import")
try:
    from flask_sqlalchemy import SQLAlchemy
    print("✅ SQLAlchemy imported")
except Exception as e:
    print(f"❌ SQLAlchemy import failed: {e}")

# Test 4: Create SQLAlchemy instance
print("Test 4: SQLAlchemy instance creation")
try:
    db = SQLAlchemy()
    print("✅ SQLAlchemy instance created")
except Exception as e:
    print(f"❌ SQLAlchemy instance failed: {e}")

# Test 5: Initialize SQLAlchemy with app
print("Test 5: SQLAlchemy initialization with app")
try:
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'  # Use SQLite for testing
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    print("✅ SQLAlchemy initialized with SQLite")
except Exception as e:
    print(f"❌ SQLAlchemy initialization failed: {e}")

# Test 6: Try MySQL connection
print("Test 6: MySQL connection test")
try:
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@2494/lK@localhost:3306/hillview_demo001?charset=utf8mb4'
    print("✅ MySQL URI configured")
except Exception as e:
    print(f"❌ MySQL configuration failed: {e}")

print("🎯 All tests completed - if you see this, the issue is elsewhere!")

# Test 7: Try to run the server briefly
print("Test 7: Server startup test")
try:
    import threading
    import time
    
    def run_server():
        app.run(debug=False, use_reloader=False, port=5001)
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    time.sleep(2)  # Wait 2 seconds
    print("✅ Server started successfully in background")
    
except Exception as e:
    print(f"❌ Server startup failed: {e}")

print("🏁 Test completed successfully!")
