#!/usr/bin/env python3
"""
Test script to verify stream loading API endpoints are working correctly
"""

import requests
import json
import time

def test_stream_api_endpoints():
    """Test both classteacher and headteacher stream API endpoints"""
    print("🧪 TESTING STREAM API ENDPOINTS")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # Test data from database check
    test_grade_id = 21  # Grade 9 with streams B, G, Y
    expected_streams = [
        {"id": 44, "name": "B"},
        {"id": 45, "name": "G"}, 
        {"id": 46, "name": "Y"}
    ]
    
    print(f"🎯 Testing with Grade ID: {test_grade_id}")
    print(f"📊 Expected streams: {len(expected_streams)} (B, G, Y)")
    
    # Test endpoints
    endpoints_to_test = [
        {
            "name": "Classteacher API",
            "url": f"{base_url}/classteacher/get_streams/{test_grade_id}",
            "expected_format": "classteacher"
        },
        {
            "name": "Headteacher Universal API", 
            "url": f"{base_url}/headteacher/universal/api/streams/{test_grade_id}",
            "expected_format": "headteacher"
        }
    ]
    
    results = []
    
    for endpoint in endpoints_to_test:
        print(f"\n🔍 Testing {endpoint['name']}")
        print(f"📡 URL: {endpoint['url']}")
        
        try:
            response = requests.get(endpoint['url'], timeout=10)
            print(f"📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON Response: {json.dumps(data, indent=2)}")
                    
                    # Validate response format
                    if endpoint['expected_format'] == 'classteacher':
                        if 'streams' in data and isinstance(data['streams'], list):
                            print(f"✅ Classteacher format valid")
                            if len(data['streams']) == 3:
                                print(f"✅ Correct number of streams: {len(data['streams'])}")
                                results.append({"endpoint": endpoint['name'], "status": "SUCCESS", "streams": len(data['streams'])})
                            else:
                                print(f"⚠️ Expected 3 streams, got {len(data['streams'])}")
                                results.append({"endpoint": endpoint['name'], "status": "PARTIAL", "streams": len(data['streams'])})
                        else:
                            print(f"❌ Invalid classteacher format")
                            results.append({"endpoint": endpoint['name'], "status": "FORMAT_ERROR", "streams": 0})
                    
                    elif endpoint['expected_format'] == 'headteacher':
                        if 'success' in data and 'streams' in data and isinstance(data['streams'], list):
                            print(f"✅ Headteacher format valid")
                            if len(data['streams']) == 3:
                                print(f"✅ Correct number of streams: {len(data['streams'])}")
                                results.append({"endpoint": endpoint['name'], "status": "SUCCESS", "streams": len(data['streams'])})
                            else:
                                print(f"⚠️ Expected 3 streams, got {len(data['streams'])}")
                                results.append({"endpoint": endpoint['name'], "status": "PARTIAL", "streams": len(data['streams'])})
                        else:
                            print(f"❌ Invalid headteacher format")
                            results.append({"endpoint": endpoint['name'], "status": "FORMAT_ERROR", "streams": 0})
                            
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON response")
                    print(f"Raw response: {response.text[:200]}...")
                    results.append({"endpoint": endpoint['name'], "status": "JSON_ERROR", "streams": 0})
                    
            elif response.status_code == 403:
                print(f"🔒 403 Forbidden - Authentication required")
                results.append({"endpoint": endpoint['name'], "status": "AUTH_ERROR", "streams": 0})
                
            elif response.status_code == 404:
                print(f"❌ 404 Not Found - Endpoint doesn't exist")
                results.append({"endpoint": endpoint['name'], "status": "NOT_FOUND", "streams": 0})
                
            else:
                print(f"❌ Unexpected status code: {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                results.append({"endpoint": endpoint['name'], "status": f"HTTP_{response.status_code}", "streams": 0})
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {str(e)}")
            results.append({"endpoint": endpoint['name'], "status": "CONNECTION_ERROR", "streams": 0})
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    success_count = 0
    for result in results:
        status_icon = "✅" if result['status'] == "SUCCESS" else "❌"
        print(f"{status_icon} {result['endpoint']}: {result['status']} ({result['streams']} streams)")
        if result['status'] == "SUCCESS":
            success_count += 1
    
    print(f"\n🎯 Overall Result: {success_count}/{len(results)} endpoints working correctly")
    
    if success_count == len(results):
        print("🎉 ALL STREAM API ENDPOINTS WORKING!")
        return True
    else:
        print("⚠️ Some endpoints need fixing")
        return False

def main():
    """Main function"""
    print("🚀 Stream API Endpoint Testing")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    success = test_stream_api_endpoints()
    
    if success:
        print("\n✅ All stream API endpoints are working correctly!")
        print("🔧 If frontend still shows 'Error loading streams', the issue is in JavaScript")
    else:
        print("\n❌ Stream API endpoints need fixing!")
        print("🔧 Check authentication, routing, and response formats")

if __name__ == "__main__":
    main()
