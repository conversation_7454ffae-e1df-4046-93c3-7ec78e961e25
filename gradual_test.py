#!/usr/bin/env python3
"""
Gradual test to identify exactly where the Hillview app hangs.
"""

import os
import sys

# Add the parent directory to the Python path so we can import new_structure as a package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

print("🔍 Gradual Hillview test...")

try:
    print("Step 1: Basic imports...")
    from flask import Flask
    print("✅ Flask imported")
    
    print("Step 2: Create Flask app...")
    app = Flask(__name__)
    print("✅ Flask app created")
    
    print("Step 3: Import new_structure.extensions...")
    from new_structure.extensions import db, csrf
    print("✅ Extensions imported")
    
    print("Step 4: Import new_structure.config...")
    from new_structure.config import config
    print("✅ Config imported")
    
    print("Step 5: Load configuration...")
    app.config.from_object(config['development'])
    print("✅ Configuration loaded")
    
    print("Step 6: Initialize extensions...")
    db.init_app(app)
    csrf.init_app(app)
    print("✅ Extensions initialized")
    
    print("Step 7: Test basic route...")
    @app.route('/test')
    def test_route():
        return "Test route working!"
    print("✅ Test route added")
    
    print("Step 8: Try to start server...")
    print("🚀 Starting server on port 5003...")
    app.run(debug=False, host='0.0.0.0', port=5003, use_reloader=False)
    
except Exception as e:
    print(f"❌ Error at step: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
