"""
Report-Based Analytics Service for the Hillview School Management System.

This service generates analytics data based on reports that have been generated by classteachers,
ensuring data consistency and reflecting the actual academic workflow.
"""
import os
import json
import pickle
import time
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Any, Optional
from ..extensions import db
from ..models.academic import Grade, Stream, Subject, Term, AssessmentType, Student, Mark
from ..models.user import Teacher
from ..services.cache_service import get_cached_report, get_cached_marksheet
from ..utils.performance import get_performance_category, get_grade_and_points

class ReportBasedAnalyticsService:
    """
    Analytics service that generates insights based on classteacher-generated reports.
    """
    
    @classmethod
    def get_available_reports(cls) -> List[Dict[str, Any]]:
        """
        Get list of all available cached reports that can be used for analytics.
        
        Returns:
            List of report metadata
        """
        available_reports = []
        
        try:
            # Check cache directories for available reports
            from ..services.cache_service import REPORT_CACHE_DIR, MARKSHEET_CACHE_DIR
            
            # Get cached reports
            if os.path.exists(REPORT_CACHE_DIR):
                for filename in os.listdir(REPORT_CACHE_DIR):
                    if filename.endswith('.pickle'):
                        try:
                            cache_key = filename.replace('.pickle', '')
                            # Parse cache key to extract metadata
                            parts = cache_key.split('_')
                            if len(parts) >= 4:
                                grade, stream, term, assessment = parts[0], parts[1], parts[2], '_'.join(parts[3:])
                                
                                # Get file modification time
                                file_path = os.path.join(REPORT_CACHE_DIR, filename)
                                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                                
                                available_reports.append({
                                    'type': 'report',
                                    'grade': grade,
                                    'stream': stream,
                                    'term': term,
                                    'assessment_type': assessment,
                                    'generated_at': mod_time.isoformat(),
                                    'cache_key': cache_key
                                })
                        except Exception as e:
                            print(f"Error parsing report cache file {filename}: {e}")
                            continue
            
            # Get cached marksheets
            if os.path.exists(MARKSHEET_CACHE_DIR):
                for filename in os.listdir(MARKSHEET_CACHE_DIR):
                    if filename.endswith('.json'):
                        try:
                            cache_key = filename.replace('.json', '')
                            parts = cache_key.split('_')
                            if len(parts) >= 4:
                                grade, stream, term, assessment = parts[0], parts[1], parts[2], '_'.join(parts[3:])
                                
                                file_path = os.path.join(MARKSHEET_CACHE_DIR, filename)
                                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                                
                                available_reports.append({
                                    'type': 'marksheet',
                                    'grade': grade,
                                    'stream': stream,
                                    'term': term,
                                    'assessment_type': assessment,
                                    'generated_at': mod_time.isoformat(),
                                    'cache_key': cache_key
                                })
                        except Exception as e:
                            print(f"Error parsing marksheet cache file {filename}: {e}")
                            continue
            
            # Sort by generation time (newest first)
            available_reports.sort(key=lambda x: x['generated_at'], reverse=True)
            
        except Exception as e:
            print(f"Error getting available reports: {e}")
        
        return available_reports
    
    @classmethod
    def get_report_based_analytics(cls, grade_filter: Optional[str] = None, 
                                 term_filter: Optional[str] = None,
                                 assessment_filter: Optional[str] = None,
                                 days_back: int = 30) -> Dict[str, Any]:
        """
        Generate analytics based on available cached reports.
        
        Args:
            grade_filter: Filter by specific grade
            term_filter: Filter by specific term
            assessment_filter: Filter by specific assessment type
            days_back: Only include reports generated in the last N days
            
        Returns:
            Analytics data based on generated reports
        """
        try:
            # Get available reports
            available_reports = cls.get_available_reports()
            
            # Filter reports based on criteria
            cutoff_date = datetime.now() - timedelta(days=days_back)
            filtered_reports = []
            
            for report in available_reports:
                report_date = datetime.fromisoformat(report['generated_at'])
                
                # Apply filters
                if report_date < cutoff_date:
                    continue
                    
                if grade_filter and report['grade'] != grade_filter:
                    continue
                    
                if term_filter and report['term'] != term_filter:
                    continue
                    
                if assessment_filter and report['assessment_type'] != assessment_filter:
                    continue
                
                filtered_reports.append(report)
            
            if not filtered_reports:
                # Fall back to live analytics when no cached reports are available
                print("No cached reports found, falling back to live analytics")
                from .academic_analytics_service import AcademicAnalyticsService

                # Get live analytics data
                live_analytics = AcademicAnalyticsService.get_comprehensive_analytics()

                return {
                    'summary': {
                        'reports_analyzed': 0,
                        'classes_covered': 0,
                        'students_analyzed': live_analytics.get('total_students_analyzed', 0),
                        'subjects_analyzed': live_analytics.get('total_subjects_analyzed', 0),
                        'average_performance': 0
                    },
                    'class_performance': [],
                    'subject_performance': live_analytics.get('subject_analytics', []),
                    'top_performers': live_analytics.get('top_performers', []),
                    'recent_activity': [],
                    'has_data': live_analytics.get('total_students_analyzed', 0) > 0,
                    'data_source': 'live_data_fallback',
                    'message': 'No cached reports found. Showing live analytics data. Generate reports through Class Teacher interface to see report-based analytics.'
                }
            
            # Process the filtered reports
            analytics_data = cls._process_report_data(filtered_reports)
            analytics_data['has_data'] = True
            analytics_data['data_source'] = 'generated_reports'
            analytics_data['reports_analyzed'] = len(filtered_reports)
            
            return analytics_data
            
        except Exception as e:
            print(f"Error generating report-based analytics: {e}")
            return {
                'error': f'Error generating analytics: {str(e)}',
                'has_data': False,
                'data_source': 'generated_reports'
            }
    
    @classmethod
    def _process_report_data(cls, reports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process cached report data to generate analytics.
        
        Args:
            reports: List of report metadata
            
        Returns:
            Processed analytics data
        """
        class_performance = []
        subject_performance = defaultdict(list)
        all_students = []
        recent_activity = []
        
        classes_covered = set()
        subjects_analyzed = set()
        
        for report_meta in reports:
            try:
                # Load the actual report data
                if report_meta['type'] == 'report':
                    report_data = get_cached_report(
                        report_meta['grade'],
                        report_meta['stream'],
                        report_meta['term'],
                        report_meta['assessment_type']
                    )
                else:  # marksheet
                    report_data = get_cached_marksheet(
                        report_meta['grade'],
                        report_meta['stream'],
                        report_meta['term'],
                        report_meta['assessment_type']
                    )
                
                if not report_data:
                    continue
                
                # Extract performance data from the report
                class_key = f"{report_meta['grade']} {report_meta['stream']}"
                classes_covered.add(class_key)
                
                # Process student data
                students_data = report_data.get('class_data', [])
                class_total = 0
                class_count = 0
                
                for student_data in students_data:
                    student_name = student_data.get('name', '')
                    student_marks = student_data.get('marks', {})
                    
                    # Calculate student average
                    valid_marks = [mark for mark in student_marks.values() if isinstance(mark, (int, float)) and mark > 0]
                    if valid_marks:
                        student_avg = sum(valid_marks) / len(valid_marks)
                        class_total += student_avg
                        class_count += 1
                        
                        all_students.append({
                            'name': student_name,
                            'average': round(student_avg, 1),
                            'grade': report_meta['grade'],
                            'stream': report_meta['stream'],
                            'term': report_meta['term'],
                            'assessment_type': report_meta['assessment_type'],
                            'subjects_count': len(valid_marks)
                        })
                        
                        # Process subject performance
                        for subject, mark in student_marks.items():
                            if isinstance(mark, (int, float)) and mark > 0:
                                subjects_analyzed.add(subject)
                                subject_performance[subject].append({
                                    'mark': mark,
                                    'grade': report_meta['grade'],
                                    'stream': report_meta['stream']
                                })
                
                # Calculate class average
                if class_count > 0:
                    class_avg = round(class_total / class_count, 1)
                    class_performance.append({
                        'class': class_key,
                        'grade': report_meta['grade'],
                        'stream': report_meta['stream'],
                        'term': report_meta['term'],
                        'assessment_type': report_meta['assessment_type'],
                        'average': class_avg,
                        'students_count': class_count,
                        'generated_at': report_meta['generated_at']
                    })
                
                # Add to recent activity
                recent_activity.append({
                    'type': 'report_generated',
                    'class': class_key,
                    'term': report_meta['term'],
                    'assessment_type': report_meta['assessment_type'],
                    'generated_at': report_meta['generated_at'],
                    'students_count': class_count
                })
                
            except Exception as e:
                print(f"Error processing report {report_meta['cache_key']}: {e}")
                continue
        
        # Calculate subject averages
        subject_averages = []
        for subject, marks_data in subject_performance.items():
            if marks_data:
                avg_mark = sum(item['mark'] for item in marks_data) / len(marks_data)
                subject_averages.append({
                    'subject': subject,
                    'average': round(avg_mark, 1),
                    'students_count': len(marks_data),
                    'performance_category': get_performance_category(avg_mark)
                })
        
        # Sort data
        all_students.sort(key=lambda x: x['average'], reverse=True)
        subject_averages.sort(key=lambda x: x['average'], reverse=True)
        class_performance.sort(key=lambda x: x['average'], reverse=True)
        recent_activity.sort(key=lambda x: x['generated_at'], reverse=True)
        
        # Calculate summary statistics
        all_averages = [student['average'] for student in all_students]
        overall_average = round(sum(all_averages) / len(all_averages), 1) if all_averages else 0
        
        return {
            'summary': {
                'reports_analyzed': len(reports),
                'classes_covered': len(classes_covered),
                'students_analyzed': len(all_students),
                'subjects_analyzed': len(subjects_analyzed),
                'average_performance': overall_average
            },
            'class_performance': class_performance,
            'subject_performance': subject_averages,
            'top_performers': all_students[:10],  # Top 10 students
            'recent_activity': recent_activity[:20]  # Last 20 activities
        }
    
    @classmethod
    def get_teacher_report_summary(cls, teacher_id: int) -> Dict[str, Any]:
        """
        Get summary of reports generated by a specific teacher.
        
        Args:
            teacher_id: ID of the teacher
            
        Returns:
            Summary of teacher's report generation activity
        """
        try:
            # This would require tracking which teacher generated which report
            # For now, return a placeholder structure
            return {
                'teacher_id': teacher_id,
                'reports_generated': 0,
                'classes_covered': [],
                'last_report_date': None,
                'total_students_reported': 0
            }
        except Exception as e:
            print(f"Error getting teacher report summary: {e}")
            return {'error': str(e)}
    
    @classmethod
    def get_analytics_dashboard_data(cls, role: str = 'classteacher', 
                                   teacher_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get analytics data for dashboard display based on user role.
        
        Args:
            role: User role (classteacher, headteacher)
            teacher_id: ID of the teacher (for classteacher role)
            
        Returns:
            Role-appropriate analytics data
        """
        try:
            if role == 'headteacher':
                # Headteacher gets school-wide analytics
                return cls.get_report_based_analytics(days_back=90)
            else:
                # Classteacher gets limited analytics
                # Filter by their assigned classes if possible
                return cls.get_report_based_analytics(days_back=30)
                
        except Exception as e:
            print(f"Error getting dashboard analytics: {e}")
            return {
                'error': str(e),
                'has_data': False,
                'data_source': 'generated_reports'
            }
