<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent Management Dashboard - Hillview School</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .parent-dashboard {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 5px solid #667eea;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .action-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
        }
        
        .action-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .action-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .action-desc {
            color: #666;
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .recent-parents {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .parent-list {
            list-style: none;
            padding: 0;
        }
        
        .parent-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }
        
        .parent-item:hover {
            background-color: #f8f9fa;
        }
        
        .parent-info {
            flex: 1;
        }
        
        .parent-name {
            font-weight: bold;
            color: #333;
        }
        
        .parent-email {
            color: #666;
            font-size: 0.9em;
        }
        
        .parent-status {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-verified {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .status-unverified {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .btn-view {
            background-color: #17a2b8;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9em;
        }
        
        .btn-view:hover {
            background-color: #138496;
        }
        
        .alerts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .alert-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #ffc107;
        }
        
        .alert-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-list {
            list-style: none;
            padding: 0;
        }
        
        .alert-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            color: #666;
        }
        
        .alert-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="parent-dashboard">
        <!-- Header -->
        <div class="dashboard-header">
            <h1><i class="fas fa-user-friends"></i> Parent Management Dashboard</h1>
            <p>Manage parent accounts and student-parent relationships</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_parents }}</div>
                <div class="stat-label">Total Parents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ active_parents }}</div>
                <div class="stat-label">Active Parents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ verified_parents }}</div>
                <div class="stat-label">Verified Parents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_links }}</div>
                <div class="stat-label">Parent-Student Links</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="actions-grid">
            <div class="action-card">
                <div class="action-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="action-title">Add New Parent</div>
                <div class="action-desc">Create a new parent account and set up login credentials</div>
                <a href="{{ url_for('parent_management.add_parent') }}" class="btn-primary">
                    <i class="fas fa-plus"></i> Add Parent
                </a>
            </div>
            
            <div class="action-card">
                <div class="action-icon">
                    <i class="fas fa-link"></i>
                </div>
                <div class="action-title">Link Parent & Student</div>
                <div class="action-desc">Connect existing parents with their children in the system</div>
                <a href="{{ url_for('parent_management.link_parent_student') }}" class="btn-primary">
                    <i class="fas fa-link"></i> Create Link
                </a>
            </div>
        </div>

        <!-- Recent Parents -->
        {% if recent_parents %}
        <div class="recent-parents">
            <h2 class="section-title">
                <i class="fas fa-clock"></i> Recent Parents
            </h2>
            <ul class="parent-list">
                {% for parent in recent_parents %}
                <li class="parent-item">
                    <div class="parent-info">
                        <div class="parent-name">{{ parent.get_full_name() }}</div>
                        <div class="parent-email">{{ parent.email }}</div>
                    </div>
                    <div class="parent-status">
                        <span class="status-badge {{ 'status-active' if parent.is_active else 'status-inactive' }}">
                            {{ 'Active' if parent.is_active else 'Inactive' }}
                        </span>
                        <span class="status-badge {{ 'status-verified' if parent.is_verified else 'status-unverified' }}">
                            {{ 'Verified' if parent.is_verified else 'Unverified' }}
                        </span>
                        <a href="{{ url_for('parent_management.view_parent', parent_id=parent.id) }}" class="btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <!-- Alerts -->
        <div class="alerts-section">
            {% if parents_without_children %}
            <div class="alert-card">
                <div class="alert-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Parents Without Children ({{ parents_without_children|length }})
                </div>
                <ul class="alert-list">
                    {% for parent in parents_without_children[:5] %}
                    <li class="alert-item">
                        {{ parent.get_full_name() }} ({{ parent.email }})
                        <a href="{{ url_for('parent_management.link_parent_student') }}" style="float: right; color: #667eea;">Link</a>
                    </li>
                    {% endfor %}
                    {% if parents_without_children|length > 5 %}
                    <li class="alert-item" style="text-align: center; font-style: italic;">
                        ... and {{ parents_without_children|length - 5 }} more
                    </li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}

            {% if students_without_parents %}
            <div class="alert-card">
                <div class="alert-title">
                    <i class="fas fa-user-graduate"></i>
                    Students Without Parents ({{ students_without_parents|length }})
                </div>
                <ul class="alert-list">
                    {% for student in students_without_parents[:5] %}
                    <li class="alert-item">
                        {{ student.name }} ({{ student.admission_number }})
                        <a href="{{ url_for('parent_management.link_parent_student') }}" style="float: right; color: #667eea;">Link</a>
                    </li>
                    {% endfor %}
                    {% if students_without_parents|length > 5 %}
                    <li class="alert-item" style="text-align: center; font-style: italic;">
                        ... and {{ students_without_parents|length - 5 }} more
                    </li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}
        </div>

        <!-- Back Button -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="{{ url_for('admin.dashboard') }}" class="btn-primary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}" style="margin-bottom: 10px; padding: 15px; border-radius: 8px; background: white; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        {{ message }}
                        <button type="button" class="close" onclick="this.parentElement.remove();" style="float: right; background: none; border: none; font-size: 1.2em; cursor: pointer;">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
</body>
</html>
