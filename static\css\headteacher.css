/* Enhanced Dashboard for Headteacher */
.dashboard-container {
  max-width: 1400px;
  margin: 90px auto 70px;
  padding: 0 var(--space-lg);
}

/* Stats Cards Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.stat-card {
  background-color: var(--color-white);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  box-shadow: var(--shadow-soft);
  transition: all var(--transition-fast);
  border-top: 4px solid var(--color-forest-green);
  text-align: center;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-dark-green);
  margin: var(--space-md) 0;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: var(--color-forest-green);
  font-weight: 500;
}

/* Performance Highlights */
.highlights-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.highlight-card {
  background-color: var(--color-white);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  box-shadow: var(--shadow-soft);
  transition: all var(--transition-fast);
  border-left: 4px solid var(--color-bright-green);
}

.highlight-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.highlight-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--color-forest-green);
  margin: var(--space-sm) 0;
}

.highlight-label {
  font-size: 1rem;
  color: var(--color-medium-dark-green);
  font-weight: 500;
  margin-bottom: var(--space-xs);
}

/* Enhanced Tables */
.data-card {
  background-color: var(--color-white);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  box-shadow: var(--shadow-soft);
  margin-bottom: var(--space-xl);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--space-md);
  margin-bottom: var(--space-lg);
  border-bottom: 2px solid var(--color-medium-gray);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-forest-green);
  margin: 0;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.data-table th {
  background-color: var(--color-forest-green);
  color: var(--color-white);
  padding: var(--space-md);
  text-align: left;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th:first-child {
  border-top-left-radius: var(--radius-sm);
}

.data-table th:last-child {
  border-top-right-radius: var(--radius-sm);
}

.data-table td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--color-medium-gray);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:hover td {
  background-color: rgba(31, 125, 83, 0.05);
}

/* Performance Categories */
.category-excellent {
  color: var(--color-success);
  font-weight: 600;
}

.category-good {
  color: #3498db;
  font-weight: 600;
}

.category-average {
  color: var(--color-warning);
  font-weight: 600;
}

.category-below {
  color: var(--color-error);
  font-weight: 600;
}

/* Gender Distribution Styling */
.gender-male {
  color: #3498db;
  font-weight: 500;
}

.gender-female {
  color: #e74c3c;
  font-weight: 500;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-md);
}

.section-header h2 {
  margin: 0;
  font-size: 1.75rem;
  color: var(--color-dark-green);
}

.section-header::after {
  content: '';
  flex: 1;
  height: 2px;
  background: linear-gradient(to right, rgba(37, 95, 56, 0.5), rgba(37, 95, 56, 0.1));
  margin-left: var(--space-md);
}

/* Performance Chart Area */
.chart-container {
  height: 300px;
  margin: var(--space-lg) 0;
  background-color: rgba(248, 249, 250, 0.7);
  border-radius: var(--radius-md);
  border: 1px dashed var(--color-medium-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: var(--color-forest-green);
  font-style: italic;
  opacity: 0.7;
}

/* Dashboard Actions */
.dashboard-actions {
  display: flex;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
  justify-content: flex-end;
}

.action-btn {
  background-color: var(--color-forest-green);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
}

.action-btn:hover {
  background-color: var(--color-bright-green);
  transform: translateY(-2px);
}

.action-btn-outline {
  background-color: transparent;
  border: 2px solid var(--color-forest-green);
  color: var(--color-forest-green);
}

.action-btn-outline:hover {
  background-color: var(--color-forest-green);
  color: var(--color-white);
}

/* Loading State */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(37, 95, 56, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-forest-green);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}