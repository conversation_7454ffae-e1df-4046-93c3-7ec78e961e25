<!-- Component Mark Entry Template -->
<div class="composite-subject-entry">
  <div class="component-header">
    <span class="component-title">{{ component.name }}</span>
    <span class="component-weight"
      >({{ (component.weight * 100) | int }}%)</span
    >
  </div>

  <div class="component-inputs">
    <div class="mark-input-container">
      <input
        type="number"
        name="component_mark_{{ student_id }}_{{ component.id }}"
        value="{{ component_mark.raw_mark | default(0) | int if component_mark else 0 }}"
        min="0"
        max="{{ component.max_raw_mark }}"
        data-max-mark="{{ component.max_raw_mark }}"
        data-weight="{{ component.weight }}"
        data-component-id="{{ component.id }}"
        data-subject-id="{{ subject_obj.id }}"
        class="component-mark-input"
        oninput="updateComponentPercentage('{{ student_id }}', '{{ component.id }}', '{{ subject_obj.id }}')"
      />
      <span class="mark-separator">/</span>
      <input
        type="number"
        name="component_max_{{ student_id }}_{{ component.id }}"
        value="{{ component.max_raw_mark }}"
        min="1"
        max="1000"
        class="component-max-input"
        oninput="updateComponentPercentage('{{ student_id }}', '{{ component.id }}', '{{ subject_obj.id }}')"
      />
    </div>

    <div class="percentage-display">
      <span
        id="component_percentage_{{ student_id }}_{{ component.id }}"
        class="percentage-value"
      >
        {{ ((component_mark.raw_mark | default(0) / component.max_raw_mark) *
        100) | round(1) if component_mark else 0 }}%
      </span>
      <div class="percentage-bar">
        <div
          class="percentage-fill"
          style="width: {{ ((component_mark.raw_mark | default(0) / component.max_raw_mark) * 100) | round(1) if component_mark else 0 }}%"
        ></div>
      </div>
    </div>
  </div>
</div>
