#!/usr/bin/env python3
"""
Test script to verify dynamic filtering functionality
Tests both classteacher and headteacher API endpoints for stream fetching
"""

import requests
import json
from requests.auth import HTTPBasicAuth

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_GRADE_ID = 1  # Assuming grade ID 1 exists

def test_classteacher_streams_endpoint():
    """Test the classteacher streams API endpoint"""
    print("🔍 Testing Classteacher Streams API...")
    
    url = f"{BASE_URL}/classteacher/get_streams/{TEST_GRADE_ID}"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if 'streams' in data:
                print(f"✅ Found {len(data['streams'])} streams")
                return True
            else:
                print("❌ No 'streams' key in response")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_headteacher_streams_endpoint():
    """Test the headteacher universal streams API endpoint"""
    print("\n🔍 Testing Headteacher Universal Streams API...")
    
    url = f"{BASE_URL}/headteacher/universal/api/streams/{TEST_GRADE_ID}"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if 'success' in data and data['success'] and 'streams' in data:
                print(f"✅ Found {len(data['streams'])} streams")
                return True
            else:
                print("❌ Invalid response format")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_database_connectivity():
    """Test basic database connectivity by checking grades"""
    print("\n🔍 Testing Database Connectivity...")

    try:
        # Create Flask app context for database access
        import sys
        import os

        # Add current directory to path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        # Import and create app context
        from run import app

        with app.app_context():
            from models.academic import Grade, Stream

            # Test database query
            grades = Grade.query.all()
            print(f"✅ Found {len(grades)} grades in database")

            for grade in grades[:5]:  # Show first 5 grades
                streams = Stream.query.filter_by(grade_id=grade.id).all()
                print(f"  - Grade {grade.id}: {grade.name} ({len(streams)} streams)")

            return True

    except Exception as e:
        print(f"❌ Database Error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Dynamic Filtering API Test Suite")
    print("=" * 50)
    
    # Test database connectivity first
    db_test = test_database_connectivity()
    
    # Test API endpoints
    classteacher_test = test_classteacher_streams_endpoint()
    headteacher_test = test_headteacher_streams_endpoint()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"Database Connectivity: {'✅ PASS' if db_test else '❌ FAIL'}")
    print(f"Classteacher API: {'✅ PASS' if classteacher_test else '❌ FAIL'}")
    print(f"Headteacher API: {'✅ PASS' if headteacher_test else '❌ FAIL'}")
    
    if all([db_test, classteacher_test, headteacher_test]):
        print("\n🎉 All tests passed! Dynamic filtering should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
