<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Edit Assignment - Hillview School</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      .edit-form-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
      }
      
      .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
      }
      
      @media (max-width: 768px) {
        .form-grid {
          grid-template-columns: 1fr;
        }
      }
      
      .form-group {
        margin-bottom: 15px;
      }
      
      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
      }
      
      .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }
      
      .form-control:focus {
        border-color: #4a6741;
        outline: none;
      }
      
      .btn-container {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
      }
      
      .save-btn {
        background-color: #4a6741;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
      }
      
      .cancel-btn {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="manage-container">
        <header class="page-header">
          <h1>Edit Assignment</h1>
          <div class="nav-links">
            <a href="{{ url_for('classteacher.manage_teachers') }}"
              >Back to Manage Teachers</a
            >
            |
            <a href="{{ url_for('classteacher.dashboard') }}"
              >Back to Dashboard</a
            >
            |
            <a href="{{ url_for('auth.logout_route') }}">Logout</a>
          </div>
        </header>

        <!-- Message container for notifications -->
        <div id="message-container">
          {% if error_message %}
          <div class="message message-error">{{ error_message }}</div>
          {% endif %} {% if success_message %}
          <div class="message message-success">{{ success_message }}</div>
          {% endif %}
        </div>

        <div class="edit-form-card">
          <h2>
            {% if assignment_type == 'class_teacher' %}
            Edit Class Teacher Assignment
            {% else %}
            Edit Subject Assignment
            {% endif %}
          </h2>
          
          <form method="POST" action="{{ url_for('bulk_assignments.update_assignment') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
            <input type="hidden" name="assignment_id" value="{{ assignment.id }}" />
            <input type="hidden" name="assignment_type" value="{{ assignment_type }}" />
            
            <div class="form-grid">
              <div class="form-group">
                <label for="teacher_id">Teacher:</label>
                <select name="teacher_id" id="teacher_id" class="form-control" required>
                  {% for teacher in teachers %}
                  <option value="{{ teacher.id }}" {% if teacher.id == assignment.teacher_id %}selected{% endif %}>
                    {{ teacher.username }}
                  </option>
                  {% endfor %}
                </select>
              </div>
              
              {% if assignment_type == 'subject' %}
              <div class="form-group">
                <label for="subject_id">Subject:</label>
                <select name="subject_id" id="subject_id" class="form-control" required>
                  {% for subject in subjects %}
                  <option value="{{ subject.id }}" {% if subject.id == assignment.subject_id %}selected{% endif %}>
                    {{ subject.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
              {% endif %}
              
              <div class="form-group">
                <label for="grade_id">Grade:</label>
                <select name="grade_id" id="grade_id" class="form-control" required onchange="updateStreams()">
                  {% for grade in grades %}
                  <option value="{{ grade.id }}" {% if grade.id == assignment.grade_id %}selected{% endif %}>
                    {{ grade.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
              
              <div class="form-group">
                <label for="stream_id">Stream (Optional):</label>
                <select name="stream_id" id="stream_id" class="form-control">
                  <option value="">-- All Streams --</option>
                  {% for stream in streams %}
                  <option value="{{ stream.id }}" {% if assignment.stream_id and stream.id == assignment.stream_id %}selected{% endif %}>
                    {{ stream.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
              
              {% if assignment_type == 'class_teacher' or assignment.is_class_teacher %}
              <div class="form-group" style="grid-column: span 2;">
                <label>
                  <input type="checkbox" name="is_class_teacher" value="1" {% if assignment.is_class_teacher %}checked{% endif %} />
                  Designate as Class Teacher
                </label>
              </div>
              {% endif %}
            </div>
            
            <div class="btn-container">
              <a href="{{ url_for('classteacher.manage_teachers') }}" class="cancel-btn">Cancel</a>
              <button type="submit" class="save-btn">Save Changes</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      // Function to get the correct teacher streams API endpoint based on current context
      function getTeacherStreamsEndpoint(gradeId) {
        // Check if we're in headteacher context (admin routes)
        if (window.location.pathname.includes('/headteacher/') ||
            window.location.pathname.includes('/admin/')) {
          return `/headteacher/api/teacher_streams/${gradeId}`;
        }
        // Default to classteacher endpoint
        return `/classteacher/teacher_streams/${gradeId}`;
      }

      // Function to extract streams from response data (handles different response formats)
      function extractStreamsFromResponse(data) {
        // Handle different response formats
        if (data.streams) {
          return data.streams; // Admin blueprint format: {streams: [...]}
        } else if (data.success && data.streams) {
          return data.streams; // Universal blueprint format: {success: true, streams: [...]}
        }
        return []; // Fallback to empty array
      }

      // Function to update streams based on selected grade
      function updateStreams() {
        const gradeId = document.getElementById('grade_id').value;
        const streamSelect = document.getElementById('stream_id');

        // Clear existing options
        streamSelect.innerHTML = '<option value="">-- All Streams --</option>';

        if (gradeId) {
          // Fetch streams for the selected grade using dynamic endpoint
          const endpoint = getTeacherStreamsEndpoint(gradeId);
          console.log("Edit assignment using endpoint:", endpoint);

          fetch(endpoint)
            .then(response => response.json())
            .then(data => {
              const streams = extractStreamsFromResponse(data);
              if (streams && streams.length > 0) {
                streams.forEach(stream => {
                  const option = document.createElement('option');
                  option.value = stream.id;
                  option.textContent = stream.name;
                  streamSelect.appendChild(option);
                });
              }
            })
            .catch(error => {
              console.error('Error fetching streams:', error);
              // Fallback: try the other endpoint if first one fails
              const fallbackEndpoint = gradeId ?
                (endpoint.includes('/headteacher/') ?
                  `/classteacher/teacher_streams/${gradeId}` :
                  `/headteacher/api/teacher_streams/${gradeId}`) : null;

              if (fallbackEndpoint) {
                console.log("Edit assignment trying fallback endpoint:", fallbackEndpoint);
                fetch(fallbackEndpoint)
                  .then(response => response.json())
                  .then(data => {
                    const streams = extractStreamsFromResponse(data);
                    if (streams && streams.length > 0) {
                      streams.forEach(stream => {
                        const option = document.createElement('option');
                        option.value = stream.id;
                        option.textContent = stream.name;
                        streamSelect.appendChild(option);
                      });
                    }
                  })
                  .catch(fallbackError => {
                    console.error('Edit assignment fallback endpoint also failed:', fallbackError);
                  });
              }
            });
        }
      }
      
      // Auto-hide messages after 5 seconds
      document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const messages = document.querySelectorAll('.message');
          messages.forEach(message => {
            message.style.display = 'none';
          });
        }, 5000);
      });
    </script>
  </body>
</html>
