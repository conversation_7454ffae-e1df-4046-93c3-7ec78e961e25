Stack trace:
Frame         Function      Args
001B9E9EA330  00021005FEBA (00021029B0AB, 00021026AB81, 000210314220, 001B9E9E7C70) msys-2.0.dll+0x1FEBA
001B9E9EA330  0002100467F9 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x67F9
001B9E9EA330  000210046832 (000000000032, 0000000018A1, 000210314220, 000A000003E0) msys-2.0.dll+0x6832
001B9E9EA330  0002100E49CF (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0xA49CF
001B9E9EA330  0002100963D3 (000200000000, 0204D1C2ED88, 000210314220, 0204FFFFFFFF) msys-2.0.dll+0x563D3
001B9E9EA330  0002100D45F3 (000000000000, 00080000F34D, 000000000001, 000000000000) msys-2.0.dll+0x945F3
001B9E9EA330  0002101949FB (000000000000, 00080000F34D, 000000000001, 000000000000) msys-2.0.dll+0x1549FB
001B9E9EA330  0004B3931961 (001B9E9EA2A8, 7FFAAED5F980, 000000000000, 0204D1C513D0) msys-magic-1.dll+0x1961
001B9E9EA330  0004B39365FE (000000000000, 000000000010, FFFFFFFFFFFFFF0, 001B9E9EA360) msys-magic-1.dll+0x65FE
001B9E9EA330  7FFAFE3C4771 (00000000000A, 001B9E9EA5A0, 7FFAFE3C11C7, 0004B3931A80) libffi-8.dll+0x4771
001B9E9EA360  7FFAFE3C4493 (000000000000, 001B9E9EA590, 001B9E9EA5E0, 000000000000) libffi-8.dll+0x4493
001B9E9EA5E0  7FFAFE3C42C2 (0204C21748C0, 0204C21748C0, 7FFAAED5F960, 001B9E9EA580) libffi-8.dll+0x42C2
001B9E9EA5E0  7FFAFE3D3C05 (0004B3931A80, 001B9E9EA590, 0204BF0B2520, 020400000000) _ctypes.pyd+0x3C05
001B9E9EA5E0  7FFAFE3D2ACA (0204D1C581E0, 0204D1BD32C0, 000000000000, 001B00001101) _ctypes.pyd+0x2ACA
000000000000  7FFAFE3D26C8 (0204D1BD33C0, 000000000000, 020400000000, 0204D1C386B0) _ctypes.pyd+0x26C8
000000000000  7FFAAE7E83DE (7FFAAE82F79F, 0204BCB91B98, 0204D1C2EEC0, 000000000000) python311.dll+0x483DE
000000000000  7FFAAE7EBDDF (001B9E9EA9D0, 000000000000, 7FFAAEBCED08, 000000000001) python311.dll+0x4BDDF
001B9E9EA9D0  7FFAAE7ED423 (0204D1C3B880, 7FFAAED5F960, 0204C2067700, 000000000000) python311.dll+0x4D423
0204D1C3B880  7FFAAE7C7FEB (7FFAAED5F960, 7FFAAEBCED08, 7FFAAEBCED08, 000000000000) python311.dll+0x27FEB
0204D1C3B880  7FFAAE892533 (0204C2067700, 0204D1C3B880, 000000000004, 0204BCB91C30) python311.dll+0xF2533
0204C2067700  7FFAAE890888 (000000000000, 7FFAAED3D148, 0204D1C3B880, 000000000000) python311.dll+0xF0888
0204D1C3B880  7FFAAE890758 (7FFAAEC16F44, 0204BCB919B0, 0204D1C64130, 0204D1BD93C0) python311.dll+0xF0758
000000000002  7FFAAE82D2EC (0204D1C60180, 0204BCD09D50, 000000000007, 000000000000) python311.dll+0x8D2EC
0204D1C60180  7FFAAE895073 (7FFAAE7CFAA2, 001B9E9EAEE0, 0204BCCD19E0, 7FFAAEBFC774) python311.dll+0xF5073
001B9E9EAEE0  7FFAAE894D40 (001B9E9EAEE0, 001B9E9EAEE0, 0204BCB91AA8, 000000000000) python311.dll+0xF4D40
001B9E9EAEE0  7FFAAE7F1F7F (0204BCCD3CE0, 0204BCB917C8, 001B9E9EB0B0, 000000000002) python311.dll+0x51F7F
7FFAAED5F960  7FFAAE876981 (7FFAAE831AED, 000000000002, 000000000000, 000000000000) python311.dll+0xD6981
001B9E9EB170  7FFAAE82B5DB (0204BCCD3CE0, 001B9E9EB130, 7FFA00000000, 0204BCCD3CE0) python311.dll+0x8B5DB
000000000000  7FFAAE82B516 (7FFAAED3F048, 0204D1C3BEF0, 0204BCD098F0, 000000000000) python311.dll+0x8B516
000000000000  7FFAAE8932D1 (000000000000, 000000000001, 0204D1C3BEF0, 000000000000) python311.dll+0xF32D1
000000000001  7FFAAE820BA5 (000000000000, 00000000007F, 000000000000, 020400000000) python311.dll+0x80BA5
000000000000  7FFAAE82C3FC (7FFAAEBDA138, 0204BCB91670, 0204D1C35DF0, 0204D1C2DA20) python311.dll+0x8C3FC
End of stack trace (more stack frames may be present)
Loaded modules:
7FF6216A0000 python.exe
7FFB31030000 ntdll.dll
7FFB30260000 KERNEL32.DLL
7FFB2E120000 KERNELBASE.dll
7FFB2E530000 ucrtbase.dll
7FFAAE7A0000 python311.dll
7FFB23D60000 VCRUNTIME140.dll
7FFB30F20000 WS2_32.dll
7FFB23400000 VERSION.dll
7FFB2F900000 RPCRT4.dll
7FFB30140000 msvcrt.dll
7FFB2E500000 bcrypt.dll
7FFB30E20000 ADVAPI32.dll
7FFB2FEE0000 sechost.dll
7FFB2EBA0000 bcryptprimitives.dll
0204BCBA0000 python3.DLL
7FFB1A5C0000 select.pyd
7FFB0EE50000 _socket.pyd
7FFB2CC00000 IPHLPAPI.DLL
7FFAFE600000 _ssl.pyd
7FFB2E790000 CRYPT32.dll
7FFAD9A70000 libcrypto-3.dll
7FFAFE530000 libssl-3.dll
7FFB2ECC0000 USER32.dll
7FFB2E900000 win32u.dll
7FFB2FA20000 GDI32.dll
7FFB2EA70000 gdi32full.dll
7FFB2EC20000 msvcp_win.dll
7FFB2F7E0000 IMM32.DLL
7FFAFE500000 _bz2.pyd
7FFAFE440000 _lzma.pyd
7FFB2A9E0000 _speedups.cp311-win_amd64.pyd
7FFB285A0000 _hashlib.pyd
7FFAFE3D0000 _ctypes.pyd
7FFB30AE0000 ole32.dll
7FFAFE3C0000 libffi-8.dll
7FFB30570000 combase.dll
7FFB2F820000 OLEAUT32.dll
7FFB180B0000 unicodedata.pyd
7FFB198E0000 _decimal.pyd
7FFB253D0000 _uuid.pyd
7FFB2EF30000 shell32.DLL
7FFB2E650000 wintypes.dll
7FFB264C0000 collections.cp311-win_amd64.pyd
7FFB198C0000 immutabledict.cp311-win_amd64.pyd
7FFB198A0000 processors.cp311-win_amd64.pyd
7FFB19880000 resultproxy.cp311-win_amd64.pyd
7FFB197B0000 util.cp311-win_amd64.pyd
7FFB09B80000 _asyncio.pyd
7FFAFE520000 _overlapped.pyd
7FFB2D670000 mswsock.dll
7FFB19710000 _greenlet.cp311-win_amd64.pyd
7FFB1A5B0000 _queue.pyd
7FFABB460000 _rust.pyd
7FFB196D0000 _cffi_backend.cp311-win_amd64.pyd
7FFB19640000 pyexpat.pyd
7FFB196B0000 _elementtree.pyd
7FFAD8820000 _imaging.cp311-win_amd64.pyd
7FFAD6ED0000 _multiarray_umath.cp311-win_amd64.pyd
7FFB18020000 msvcp140-263139962577ecda4cd9469ca360a746.dll
7FFABA0F0000 libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll
7FFB2A9D0000 VCRUNTIME140_1.dll
7FFB19620000 _umath_linalg.cp311-win_amd64.pyd
7FFB195F0000 bit_generator.cp311-win_amd64.pyd
7FFB195C0000 _common.cp311-win_amd64.pyd
7FFB17E50000 mtrand.cp311-win_amd64.pyd
7FFB17FD0000 _bounded_integers.cp311-win_amd64.pyd
7FFB18210000 _mt19937.cp311-win_amd64.pyd
7FFB17FB0000 _philox.cp311-win_amd64.pyd
7FFB17E10000 _pcg64.cp311-win_amd64.pyd
7FFB17D90000 _sfc64.cp311-win_amd64.pyd
7FFB08340000 _generator.cp311-win_amd64.pyd
7FFB28470000 pandas_parser.cp311-win_amd64.pyd
7FFB254E0000 pandas_datetime.cp311-win_amd64.pyd
7FFAE9F30000 interval.cp311-win_amd64.pyd
7FFAD8650000 hashtable.cp311-win_amd64.pyd
7FFB08300000 missing.cp311-win_amd64.pyd
7FFB0A7A0000 dtypes.cp311-win_amd64.pyd
7FFB0A460000 ccalendar.cp311-win_amd64.pyd
7FFB09B60000 np_datetime.cp311-win_amd64.pyd
7FFB082C0000 conversion.cp311-win_amd64.pyd
7FFB08FB0000 base.cp311-win_amd64.pyd
7FFAF9A00000 offsets.cp311-win_amd64.pyd
7FFB07C90000 timestamps.cp311-win_amd64.pyd
7FFB080F0000 nattype.cp311-win_amd64.pyd
7FFB07570000 timedeltas.cp311-win_amd64.pyd
7FFB07A20000 timezones.cp311-win_amd64.pyd
7FFB241A0000 _zoneinfo.pyd
0204D0F20000 tzres.dll
7FFAFBF00000 fields.cp311-win_amd64.pyd
7FFAFBEC0000 tzconversion.cp311-win_amd64.pyd
7FFB08050000 properties.cp311-win_amd64.pyd
7FFAF9D20000 parsing.cp311-win_amd64.pyd
7FFAFA2F0000 strptime.cp311-win_amd64.pyd
7FFAEDBC0000 period.cp311-win_amd64.pyd
7FFAF9CE0000 vectorized.cp311-win_amd64.pyd
7FFB07C30000 ops_dispatch.cp311-win_amd64.pyd
7FFAD6D00000 algos.cp311-win_amd64.pyd
7FFAED580000 lib.cp311-win_amd64.pyd
7FFAF7CE0000 ops.cp311-win_amd64.pyd
7FFAFA3E0000 hashing.cp311-win_amd64.pyd
7FFB07550000 arrays.cp311-win_amd64.pyd
7FFAEDB70000 tslib.cp311-win_amd64.pyd
7FFAE9E50000 sparse.cp311-win_amd64.pyd
7FFAED530000 internals.cp311-win_amd64.pyd
7FFAFCCE0000 indexing.cp311-win_amd64.pyd
7FFAE98F0000 index.cp311-win_amd64.pyd
7FFAEB760000 writers.cp311-win_amd64.pyd
7FFAE9690000 join.cp311-win_amd64.pyd
7FFAEAF60000 aggregations.cp311-win_amd64.pyd
7FFAEA520000 msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll
7FFAEDB40000 indexers.cp311-win_amd64.pyd
7FFAEB720000 reshape.cp311-win_amd64.pyd
7FFAD6770000 groupby.cp311-win_amd64.pyd
7FFAFC9D0000 json.cp311-win_amd64.pyd
7FFAE9BC0000 parsers.cp311-win_amd64.pyd
7FFAF9900000 testing.cp311-win_amd64.pyd
7FFB19680000 _sqlite3.pyd
7FFB085D0000 sqlite3.dll
0004B3930000 msys-magic-1.dll
000461220000 msys-bz2-1.dll
00049FA70000 msys-lzma-5.dll
000210040000 msys-2.0.dll
000522FE0000 msys-z.dll
00048B870000 msys-zstd-1.dll
7FFB2D9A0000 CRYPTBASE.DLL
