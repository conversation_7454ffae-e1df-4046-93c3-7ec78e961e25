<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promotion History - {{ school_info.school_name or 'Hillview School' }}</title>
    
    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Modern CSS Framework -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern_classteacher.css') }}">
    
    <style>
        .page-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: var(--space-6);
        }

        .history-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            margin-bottom: var(--space-6);
            box-shadow: var(--shadow-xl);
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-6);
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: #667eea;
            margin-bottom: var(--space-2);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .history-table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-xl);
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
        }

        .history-table th,
        .history-table td {
            padding: var(--space-4);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .history-table th {
            background: rgba(102, 126, 234, 0.1);
            font-weight: 600;
            color: var(--text-primary);
        }

        .history-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .promotion-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-promoted {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .badge-repeated {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .badge-transferred {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .badge-graduated {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }

        .btn-back {
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-lg);
            font-weight: 600;
            border: none;
            cursor: pointer;
            background: #667eea;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            transition: all 0.3s ease;
        }

        .btn-back:hover {
            background: #764ba2;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="page-container">
        <div class="content-wrapper">
            <!-- Header -->
            <div class="history-header">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-4);">
                    <div>
                        <h1 style="margin: 0; color: var(--text-primary); font-size: 2rem; font-weight: 800;">
                            <i class="fas fa-history" style="color: #667eea; margin-right: var(--space-3);"></i>
                            Promotion History
                        </h1>
                        <p style="margin: var(--space-2) 0 0; color: var(--text-secondary); font-size: 1rem;">
                            Track student promotion records and statistics
                        </p>
                    </div>
                    <div>
                        <a href="{{ url_for('admin.student_promotion') }}" class="btn-back">
                            <i class="fas fa-arrow-left"></i>
                            Back to Promotions
                        </a>
                    </div>
                </div>

                <!-- Statistics -->
                {% if statistics.success %}
                <div class="statistics-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ statistics.total_promotions or 0 }}</div>
                        <div class="stat-label">Total Promotions</div>
                    </div>
                    {% for promotion_type, count in statistics.promotion_counts.items() %}
                    <div class="stat-card">
                        <div class="stat-number">{{ count }}</div>
                        <div class="stat-label">{{ promotion_type.title() }}</div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- History Table -->
            <div class="history-table-container">
                <h2 style="margin: 0 0 var(--space-6) 0; color: var(--text-primary); font-size: 1.5rem; font-weight: 700;">
                    Recent Promotion Records
                </h2>
                
                {% if history_records %}
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Student</th>
                            <th>Promotion Summary</th>
                            <th>Type</th>
                            <th>Academic Year</th>
                            <th>Promoted By</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in history_records %}
                        <tr>
                            <td>
                                {% if record.promotion_date %}
                                    {{ record.promotion_date[:10] }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td style="font-weight: 600;">
                                Student ID: {{ record.student_id }}
                            </td>
                            <td>{{ record.promotion_summary or 'N/A' }}</td>
                            <td>
                                <span class="promotion-badge badge-{{ record.promotion_type }}">
                                    {{ record.promotion_type }}
                                </span>
                            </td>
                            <td>
                                {{ record.academic_year_from }}
                                {% if record.academic_year_to and record.academic_year_to != record.academic_year_from %}
                                    → {{ record.academic_year_to }}
                                {% endif %}
                            </td>
                            <td>{{ record.promoted_by or 'System' }}</td>
                            <td>
                                <span title="{{ record.promotion_notes or '' }}">
                                    {% if record.promotion_notes %}
                                        {{ record.promotion_notes[:50] }}{% if record.promotion_notes|length > 50 %}...{% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <div style="text-align: center; padding: var(--space-8);">
                    <i class="fas fa-inbox" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: var(--space-4);"></i>
                    <h3 style="color: var(--text-primary); margin-bottom: var(--space-2);">No Promotion History</h3>
                    <p style="color: var(--text-secondary);">No promotion records found. Start by processing student promotions.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
