#!/usr/bin/env python3
"""
Test individual view imports to identify the problematic one.
"""

import os
import sys

# Add the parent directory to the Python path so we can import new_structure as a package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

print("🔍 Testing individual view imports...")

views_to_test = [
    ('auth', 'auth_bp'),
    ('teacher', 'teacher_bp'),
    ('classteacher', 'classteacher_bp'),
    ('admin', 'admin_bp'),
    ('bulk_assignments', 'bulk_assignments_bp'),
    ('setup', 'setup_bp'),
    ('staff_management', 'staff_bp'),
    ('permission_management', 'permission_bp'),
    ('headteacher_universal', 'universal_bp'),
    ('analytics_api', 'analytics_api_bp'),
    ('school_setup', 'school_setup_bp'),
    ('subject_config_api', 'subject_config_api'),
    ('missing_routes', 'missing_routes_bp'),
]

for view_name, blueprint_name in views_to_test:
    try:
        print(f"Testing import: new_structure.views.{view_name}...")
        module = __import__(f'new_structure.views.{view_name}', fromlist=[blueprint_name])
        blueprint = getattr(module, blueprint_name)
        print(f"✅ {view_name} imported successfully - blueprint: {blueprint.name}")
    except Exception as e:
        print(f"❌ {view_name} failed: {e}")
        import traceback
        traceback.print_exc()
        break

print("🎉 View import test completed!")
