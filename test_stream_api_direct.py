#!/usr/bin/env python3
"""
Direct test of the stream API endpoints to identify authentication issues.
"""

import requests
import json

def test_stream_endpoints():
    """Test the stream API endpoints directly."""
    base_url = "http://localhost:5000"
    
    print("=" * 60)
    print("🔍 DIRECT API ENDPOINT TEST")
    print("=" * 60)
    
    # Test endpoints
    endpoints = [
        "/classteacher/get_grade_streams/21",
        "/universal/api/streams/21",
        "/admin/api/streams/21"
    ]
    
    session = requests.Session()
    
    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\n🧪 Testing: {endpoint}")
        print(f"URL: {url}")
        
        try:
            # Test without authentication
            response = session.get(url)
            print(f"Status Code: {response.status_code}")
            print(f"Content Type: {response.headers.get('content-type', 'N/A')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ SUCCESS: {json.dumps(data, indent=2)}")
                except:
                    print(f"✅ SUCCESS (HTML): {response.text[:200]}...")
            elif response.status_code == 403:
                print(f"❌ FORBIDDEN: {response.text[:200]}...")
            elif response.status_code == 401:
                print(f"❌ UNAUTHORIZED: {response.text[:200]}...")
            elif response.status_code == 404:
                print(f"❌ NOT FOUND: {response.text[:200]}...")
            else:
                print(f"❌ ERROR {response.status_code}: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print("❌ CONNECTION ERROR: Flask app may not be running")
        except Exception as e:
            print(f"❌ EXCEPTION: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS:")
    print("=" * 60)
    print("If all endpoints return 403 Forbidden, the issue is authentication.")
    print("If some work and others don't, the issue is with specific decorators.")
    print("If connection fails, the Flask app is not running on localhost:5000.")

if __name__ == '__main__':
    test_stream_endpoints()
