<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Report - Kirima Primary School</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .report-container {
            max-width: 1200px;
            margin: 80px auto 30px;
            padding: 20px;
        }
        .report-header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .report-title {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .report-subtitle {
            color: #7f8c8d;
            font-size: 14px;
        }
        .performance-summary {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .summary-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .summary-item {
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            transition: transform 0.2s;
        }
        .summary-item:hover {
            transform: translateY(-3px);
        }
        .summary-count {
            font-size: 28px;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
        }
        .summary-label {
            font-size: 14px;
            color: #34495e;
        }
        .exceeding {
            background-color: rgba(46, 204, 113, 0.1);
            border: 1px solid rgba(46, 204, 113, 0.3);
        }
        .meeting {
            background-color: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
        }
        .approaching {
            background-color: rgba(241, 196, 15, 0.1);
            border: 1px solid rgba(241, 196, 15, 0.3);
        }
        .below {
            background-color: rgba(231, 76, 60, 0.1);
            border: 1px solid rgba(231, 76, 60, 0.3);
        }
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .report-table th {
            background-color: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
        }
        .report-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #ecf0f1;
        }
        .report-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .report-table tr:hover {
            background-color: #ecf0f1;
        }
        .performance-cell {
            font-weight: 500;
            border-radius: 4px;
            padding: 4px 8px;
            text-align: center;
        }
        .performance-exceeding {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        .performance-meeting {
            background-color: rgba(52, 152, 219, 0.2);
            color: #2980b9;
        }
        .performance-approaching {
            background-color: rgba(241, 196, 15, 0.2);
            color: #f39c12;
        }
        .performance-below {
            background-color: rgba(231, 76, 60, 0.2);
            color: #c0392b;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .download-btn:hover {
            background-color: #2980b9;
        }
        .mean-score {
            font-weight: 600;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <a href="#" class="navbar-brand">Kirima Primary School</a>
        <ul class="navbar-nav">
            <li class="nav-item"><a href="{{ url_for('teacher.dashboard') }}" class="nav-link">Dashboard</a></li>
            <li class="nav-item"><a href="#" class="nav-link">Reports</a></li>
            <li class="nav-item"><a href="#" class="nav-link">Help</a></li>
            <li class="nav-item"><a href="{{ url_for('auth.logout_route') }}" class="logout-btn">Logout</a></li>
        </ul>
    </nav>

    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">{{ subject }} - Grade {{ grade }} Stream {{ stream[-1] }}</h1>
            <div class="report-subtitle">
                {{ education_level }} | {{ term|replace('_', ' ')|title }} | {{ assessment_type|title }}
            </div>
        </div>

        <div class="performance-summary">
            <h3 class="summary-title">Performance Summary</h3>
            <div class="summary-grid">
                <div class="summary-item exceeding">
                    <span class="summary-count">{{ performance_summary.get('Exceeding Expectation', 0) }}</span>
                    <span class="summary-label">Exceeding Expectation</span>
                </div>
                <div class="summary-item meeting">
                    <span class="summary-count">{{ performance_summary.get('Meeting Expectation', 0) }}</span>
                    <span class="summary-label">Meeting Expectation</span>
                </div>
                <div class="summary-item approaching">
                    <span class="summary-count">{{ performance_summary.get('Approaching Expectation', 0) }}</span>
                    <span class="summary-label">Approaching Expectation</span>
                </div>
                <div class="summary-item below">
                    <span class="summary-count">{{ performance_summary.get('Below Expectation', 0) }}</span>
                    <span class="summary-label">Below Expectation</span>
                </div>
            </div>
            <div class="mean-score">
                Class Average: {{ "%.2f"|format(mean_score) }} / {{ total_marks }} ({{ "%.1f"|format(mean_percentage) }}%) - 
                <span class="performance-{{ mean_performance|replace(' ', '-')|lower }}">
                    {{ mean_performance }}
                </span>
            </div>
        </div>

        <table class="report-table">
            <thead>
                <tr>
                    <th>Student Name</th>
                    <th>Marks (Out of {{ total_marks }})</th>
                    <th>Percentage</th>
                    <th>Performance Level</th>
                </tr>
            </thead>
            <tbody>
                {% for item in data %}
                <tr>
                    <td>{{ item[0] }}</td>
                    <td>{{ item[1] }}</td>
                    <td>{{ item[2] }}%</td>
                    <td>
                        <span class="performance-cell performance-{{ item[3]|replace(' ', '-')|lower }}">
                            {{ item[3] }}
                        </span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="text-center">
            <a href="{{ url_for('generate_pdf', grade=grade, stream=stream, subject=subject) }}" class="download-btn">
                Download PDF Report
            </a>
        </div>
    </div>

    <footer>
        <p>© 2025 Kirima Primary School - All Rights Reserved</p>
    </footer>
</body>
</html>