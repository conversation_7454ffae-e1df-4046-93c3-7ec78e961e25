#!/usr/bin/env python3
"""
Test script to verify session authentication for stream API endpoints
"""

import requests
import json
import time

def test_session_authentication():
    """Test session authentication by logging in first, then testing API endpoints"""
    print("🧪 TESTING SESSION AUTHENTICATION FOR STREAM APIs")
    print("=" * 70)
    
    base_url = "http://localhost:5000"
    session = requests.Session()  # Use session to maintain cookies
    
    # Step 1: Test login page accessibility
    print("1️⃣ Testing login page accessibility...")
    try:
        response = session.get(f"{base_url}/admin_login")
        if response.status_code == 200:
            print("✅ Admin login page accessible")
        else:
            print(f"❌ Admin login page error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to access login page: {e}")
        return False
    
    # Step 2: Test headteacher login (you'll need to provide credentials)
    print("\n2️⃣ Testing headteacher login...")
    print("⚠️ Note: This test requires valid headteacher credentials")
    print("📝 Please manually login via browser and then test API endpoints")
    
    # Step 3: Test API endpoints without authentication (should fail)
    print("\n3️⃣ Testing API endpoints without authentication...")
    test_grade_id = 21  # Grade 9 with streams
    
    endpoints = [
        f"{base_url}/classteacher/get_streams/{test_grade_id}",
        f"{base_url}/headteacher/universal/api/streams/{test_grade_id}"
    ]
    
    for endpoint in endpoints:
        try:
            response = session.get(endpoint)
            print(f"📡 {endpoint}")
            print(f"   Status: {response.status_code}")
            if response.status_code == 403:
                print("   ✅ Correctly requires authentication")
            elif response.status_code == 200:
                print("   ⚠️ Unexpectedly accessible without auth")
            else:
                print(f"   ❓ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
    
    # Step 4: Test session debug endpoint
    print("\n4️⃣ Testing session debug...")
    try:
        response = session.get(f"{base_url}/debug_session")
        if response.status_code == 200:
            data = response.json()
            print("✅ Session debug accessible:")
            print(f"   Session data: {data}")
        else:
            print(f"❌ Session debug failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Session debug error: {e}")
    
    return True

def test_browser_session_simulation():
    """Simulate browser session with proper headers"""
    print("\n🌐 TESTING BROWSER SESSION SIMULATION")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Headers that mimic a browser request
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'X-Requested-With': 'XMLHttpRequest',  # Important for AJAX detection
        'Referer': f'{base_url}/headteacher/universal/manage_students',
        'Connection': 'keep-alive'
    }
    
    test_grade_id = 21
    endpoint = f"{base_url}/headteacher/universal/api/streams/{test_grade_id}"
    
    print(f"📡 Testing: {endpoint}")
    print("🔧 With browser-like headers:")
    for key, value in headers.items():
        print(f"   {key}: {value[:50]}...")
    
    try:
        response = requests.get(endpoint, headers=headers)
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 403:
            print("🔒 Still requires authentication (expected)")
        elif response.status_code == 200:
            print("✅ Success! Headers helped")
            try:
                data = response.json()
                print(f"📊 Response Data: {json.dumps(data, indent=2)}")
            except:
                print(f"📊 Response Text: {response.text[:200]}...")
        else:
            print(f"❓ Unexpected status: {response.status_code}")
            print(f"📊 Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def main():
    """Main function"""
    print("🚀 Session Authentication Testing")
    print("=" * 70)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    test_session_authentication()
    test_browser_session_simulation()
    
    print("\n" + "=" * 70)
    print("📋 NEXT STEPS:")
    print("=" * 70)
    print("1. Login as headteacher via browser: http://localhost:5000/admin_login")
    print("2. Navigate to Universal Access → Manage Students")
    print("3. Open browser developer tools (F12)")
    print("4. Select Grade 9 and watch Network tab for API calls")
    print("5. Check if stream API calls include proper session cookies")
    print("6. Look for any JavaScript errors in Console tab")
    
    print("\n🔧 DEBUGGING TIPS:")
    print("- Check if session cookies are being sent with AJAX requests")
    print("- Verify the correct API endpoint URLs are being called")
    print("- Look for CSRF token issues if implemented")
    print("- Check browser console for JavaScript errors")

if __name__ == "__main__":
    main()
