#!/usr/bin/env python3
"""
Test Flask Blueprint import in isolation.
"""

print("🔍 Flask Blueprint test...")

try:
    print("Step 1: Import Flask...")
    from flask import Flask
    print("✅ Flask imported")
    
    print("Step 2: Import Blueprint...")
    from flask import Blueprint
    print("✅ Blueprint imported")
    
    print("Step 3: Create Blueprint...")
    test_bp = Blueprint('test', __name__)
    print("✅ Blueprint created")
    
    print("Step 4: Add route to Blueprint...")
    @test_bp.route('/test')
    def test_route():
        return "Test"
    print("✅ Route added")
    
    print("🎯 Flask Blueprint test passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
