#!/usr/bin/env python3
"""
Test blueprint imports step by step to identify the hanging issue.
"""

import os
import sys

# Add the parent directory to the Python path so we can import new_structure as a package
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

print("🔍 Blueprint import test...")

try:
    print("Step 1: Basic setup...")
    from flask import Flask
    from new_structure.extensions import db, csrf
    from new_structure.config import config
    
    app = Flask(__name__)
    app.config.from_object(config['development'])
    db.init_app(app)
    csrf.init_app(app)
    print("✅ Basic setup complete")
    
    print("Step 2: Test auth blueprint import...")
    from new_structure.views.auth import auth_bp
    print("✅ Auth blueprint imported")
    
    print("Step 3: Register auth blueprint...")
    app.register_blueprint(auth_bp)
    print("✅ Auth blueprint registered")
    
    print("Step 4: Test headteacher blueprint import...")
    from new_structure.views.headteacher import headteacher_bp
    print("✅ Headteacher blueprint imported")
    
    print("Step 5: Register headteacher blueprint...")
    app.register_blueprint(headteacher_bp, url_prefix='/headteacher')
    print("✅ Headteacher blueprint registered")
    
    print("Step 6: Test teacher blueprint import...")
    from new_structure.views.teacher import teacher_bp
    print("✅ Teacher blueprint imported")
    
    print("Step 7: Register teacher blueprint...")
    app.register_blueprint(teacher_bp, url_prefix='/teacher')
    print("✅ Teacher blueprint registered")
    
    print("🎯 All blueprint tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
